"""
from person_suit.core.constants.fixed_point_scale import PRIO_LOW, PRIO_HIGH
CAW Integration Module
=====================

This module provides the bridge between the actor system and meta-systems,
ensuring proper CAW principle implementation and context propagation.

Features:
- Actor-based meta-system coordination
- UnifiedContext propagation through actor messages
- CAW-aligned resource management
- Performance optimization for actor-meta-system communication

File Purpose: Bridge actor system with meta-systems for optimal CAW alignment
Related Files:
- person_suit/core/actors/actor_system.py - Core actor system
- person_suit/core/context/unified.py - UnifiedContext implementation
- person_suit/core/application/context.py - Application context
Dependencies: asyncio, logging, typing
"""

import asyncio
import logging
from dataclasses import dataclass
from typing import Any
from typing import Dict
from typing import Optional
from typing import Protocol
from typing import runtime_checkable

from ..actors.actor_system import ActorSystem
from ..context.unified import UnifiedContext
from .interfaces.config import ApplicationConfig

logger = logging.getLogger(__name__)


@runtime_checkable
class CAWIntegratedMetaSystem(Protocol):
    """Protocol for meta-systems that support CAW integration."""

    async def initialize_with_context(self, context: UnifiedContext) -> None:
        """Initialize with CAW context."""
        ...

    async def process_with_context(self, data: Any, context: UnifiedContext) -> Any:
        """Process data with CAW context propagation."""
        ...

    async def shutdown_gracefully(self, context: UnifiedContext) -> None:
        """Shutdown gracefully with context."""
        ...


@dataclass
class MetaSystemActorConfig:
    """Configuration for meta-system actors."""

    meta_system_type: str  # "persona_core", "analyst", "predictor"
    context_propagation_enabled: bool = True
    acf_optimization_enabled: bool = True
    resource_management_enabled: bool = True
    performance_monitoring_enabled: bool = True


class CAWMetaSystemActor:
    """
    Enhanced actor wrapper for meta-systems with full CAW integration.

    This actor provides:
    - UnifiedContext propagation
    - ACF-aware processing
    - Resource-aware execution
    - Performance monitoring
    """

    def __init__(
        self,
        actor_id: str,
        meta_system: CAWIntegratedMetaSystem,
        config: MetaSystemActorConfig,
        base_context: UnifiedContext,
    ):
        self.actor_id = actor_id
        self.meta_system = meta_system
        self.config = config
        self.base_context = base_context._copy() if hasattr(base_context, "_copy") else base_context

        # Initialize performance metrics
        self.performance_metrics = {
            "messages_processed": 0,
            "processing_time_total": 0.0,
            "context_propagations": 0,
            "acf_adaptations": 0,
        }

        # Initialize context for this actor - ensure it's never None
        self.processing_context: UnifiedContext = self._create_actor_context()

        logger.info(f"✅ CAWMetaSystemActor created: {actor_id} ({config.meta_system_type})")

    def _create_actor_context(self) -> UnifiedContext:
        """Create a specialized context for this actor."""
        actor_context = UnifiedContext(
            domain=f"meta_system_{self.config.meta_system_type}",
            priority=PRIO_HIGH if self.config.meta_system_type == "persona_core" else "normal",
        )

        # Set actor-specific properties
        actor_context.set_property("actor_id", self.actor_id)
        actor_context.set_property("meta_system_type", self.config.meta_system_type)
        actor_context.set_property("caw_integration_enabled", True)

        # Configure ACF based on meta-system type
        if self.config.meta_system_type == "analyst":
            actor_context.acf_setting.fidelity_level = 0.85  # High fidelity for analysis
        elif self.config.meta_system_type == "predictor":
            actor_context.acf_setting.fidelity_level = 0.80  # Slightly lower for prediction speed
        elif self.config.meta_system_type == "persona_core":
            actor_context.acf_setting.fidelity_level = 0.90  # Highest for core personality

        return actor_context

    async def process_message(self, message: Dict[str, Any]) -> Any:
        """
        Process a message with full CAW context propagation.

        Args:
            message: Message containing data and optional context

        Returns:
            Processing result with enhanced context
        """
        start_time = asyncio.get_event_loop().time()

        try:
            # Extract or create context from message
            message_context = self._extract_context_from_message(message)

            # Compose contexts for enhanced processing
            composed_context = self.processing_context.compose(message_context)

            # Apply ACF optimization if enabled
            if self.config.acf_optimization_enabled:
                workload_characteristics = self._analyze_workload(message)
                adapted_fidelity = composed_context.adapt_fidelity(workload_characteristics)
                logger.debug(f"ACF adapted fidelity to {adapted_fidelity:.3f} for {self.actor_id}")

            # Process with enhanced context
            result = await self.meta_system.process_with_context(
                message.get("data"), composed_context
            )

            # Update performance metrics
            processing_time = asyncio.get_event_loop().time() - start_time
            self._update_performance_metrics(processing_time, composed_context)

            # Return result with context metadata
            return {
                "result": result,
                "context_metadata": {
                    "processing_fidelity": composed_context.acf_setting.fidelity_level,
                    "wave_particle_ratio": composed_context.wave_particle_ratio,
                    "processing_time": processing_time,
                    "actor_id": self.actor_id,
                },
            }

        except Exception as e:
            logger.error(f"❌ Error processing message in {self.actor_id}: {e}")
            # Return error with context for debugging
            return {
                "error": str(e),
                "context_metadata": {"actor_id": self.actor_id, "processing_failed": True},
            }

    def _extract_context_from_message(self, message: Dict[str, Any]) -> UnifiedContext:
        """Extract or create context from message."""
        if "context" in message and isinstance(message["context"], UnifiedContext):
            return message["context"]

        # Create default context for message
        msg_context = UnifiedContext(
            domain="message_processing", priority=message.get("priority", "normal")
        )

        # Set message-specific properties
        msg_context.set_property("message_type", message.get("type", "unknown"))
        msg_context.set_property("message_size", len(str(message)))

        return msg_context

    def _analyze_workload(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze workload characteristics for ACF optimization."""
        data_size = len(str(message.get("data", "")))

        return {
            "complexity": min(1.0, data_size / 1000),  # Normalize based on data size
            "urgency": PRIO_HIGH if message.get("priority") == "high" else PRIO_LOW,
            "accuracy_requirement": 0.9 if self.config.meta_system_type == "analyst" else 0.7,
            "data_size": data_size / 100,  # Normalize
        }

    def _update_performance_metrics(self, processing_time: float, context: UnifiedContext) -> None:
        """Update performance metrics for monitoring."""
        self.performance_metrics["messages_processed"] += 1
        self.performance_metrics["processing_time_total"] += processing_time
        self.performance_metrics["context_propagations"] += 1

        if hasattr(context, "properties") and context.properties.get("last_acf_adaptation"):
            self.performance_metrics["acf_adaptations"] += 1

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary for monitoring."""
        total_messages = self.performance_metrics["messages_processed"]

        return {
            "actor_id": self.actor_id,
            "meta_system_type": self.config.meta_system_type,
            "messages_processed": total_messages,
            "average_processing_time": (
                self.performance_metrics["processing_time_total"] / max(1, total_messages)
            ),
            "context_propagations": self.performance_metrics["context_propagations"],
            "acf_adaptations": self.performance_metrics["acf_adaptations"],
            "current_fidelity": self.processing_context.acf_setting.fidelity_level,
            "current_wave_particle_ratio": self.processing_context.wave_particle_ratio,
        }


class ApplicationManager:
    """
    Manager for CAW-integrated application with actor-meta-system coordination.

    This manager:
    - Coordinates actor system with standard meta-systems
    - Ensures proper context propagation
    - Manages resource allocation across systems
    - Provides unified monitoring and metrics
    """

    def __init__(
        self,
        actor_system: ActorSystem,
        application_context: ApplicationConfig,
        base_caw_context: UnifiedContext,
    ):
        self.actor_system = actor_system
        self.app_context = application_context
        self.base_context = base_caw_context
        self.meta_system_actors: Dict[str, CAWMetaSystemActor] = {}
        self.integration_metrics = {
            "actor_messages_sent": 0,
            "context_propagations": 0,
            "performance_optimizations": 0,
        }

    async def initialize_meta_system_actors(self) -> None:
        """Initialize meta-system actors with CAW integration."""
        try:
            logger.info("🎭 Initializing CAW-integrated meta-system actors...")

            # This would create actor-based versions of meta-systems
            # For now, we'll log the initialization framework

            meta_systems = ["persona_core", "analyst", "predictor"]

            for meta_system_type in meta_systems:
                MetaSystemActorConfig(
                    meta_system_type=meta_system_type,
                    context_propagation_enabled=True,
                    acf_optimization_enabled=True,
                )

                # Create context for this meta-system
                meta_context = self.base_context.restrict(meta_system_type)

                logger.info(f"  ✅ {meta_system_type} actor framework ready")
                logger.info(f"     • Context domain: {meta_context.domain}")
                logger.info(f"     • ACF fidelity: {meta_context.acf_setting.fidelity_level:.3f}")
                logger.info(f"     • Wave-particle ratio: {meta_context.wave_particle_ratio:.3f}")

            logger.info("🎯 Meta-system actor integration framework operational")

        except Exception as e:
            logger.error(f"❌ Failed to initialize meta-system actors: {e}")
            raise

    async def send_coordinated_message(
        self, target_meta_system: str, message_data: Any, context: Optional[UnifiedContext] = None
    ) -> Any:
        """Send a message with coordinated context propagation."""
        if context is None:
            context = self.base_context

        # Enhance context for coordination
        coord_context = context.compose(
            UnifiedContext(domain=f"coordination_{target_meta_system}", priority=PRIO_HIGH)
        )

        # Log coordination
        logger.debug(f"🔗 Coordinated message to {target_meta_system}")
        logger.debug(f"   • Context fidelity: {coord_context.acf_setting.fidelity_level:.3f}")
        logger.debug(f"   • Wave-particle ratio: {coord_context.wave_particle_ratio:.3f}")

        self.integration_metrics["actor_messages_sent"] += 1
        self.integration_metrics["context_propagations"] += 1

        # For now, return coordination metadata
        return {
            "coordination_successful": True,
            "target_meta_system": target_meta_system,
            "context_propagated": True,
            "coordination_context": coord_context.to_dict(),
        }

    def get_integration_status(self) -> Dict[str, Any]:
        """Get overall integration status and metrics."""
        return {
            "actor_system_available": self.actor_system is not None,
            "meta_system_actors_count": len(self.meta_system_actors),
            "integration_metrics": self.integration_metrics,
            "base_context_fidelity": self.base_context.acf_setting.fidelity_level,
            "context_propagation_enabled": True,
            "caw_principles_active": {
                "duality": True,
                "contextual_computation": True,
                "acf": True,
                "resource_management": True,
                "effect_tracking": bool(self.actor_system),
            },
        }


# Utility functions for CAW integration


def create_caw_optimized_context(
    domain: str,
    parent_context: UnifiedContext,
    meta_system_type: Optional[str] = None,
    operation_type: Optional[str] = None
) -> UnifiedContext:
    """
    Create a CAW-optimized context for specific operations.

    This implementation follows Principle II (Contextual Supremacy) by requiring
    a parent context for proper context propagation and inheritance.

    Args:
        domain: Domain for the new context
        parent_context: Parent context to inherit from (REQUIRED)
        meta_system_type: Optional meta-system type for optimization
        operation_type: Optional operation type for optimization

    Returns:
        UnifiedContext: New context optimized for the specified operation

    Raises:
        ValueError: If parent_context is None
    """
    if parent_context is None:
        raise ValueError("parent_context is required for context creation (Principle II)")

    # Base context configuration inheriting from parent
    if operation_type == "critical":
        context = UnifiedContext.create_high_fidelity(domain)
    elif operation_type == "background":
        context = UnifiedContext.create_low_resource(domain)
    else:
        context = UnifiedContext.create_default(domain)

    # Inherit capabilities and security context from parent
    context.capabilities.extend(parent_context.capabilities)
    context.security_context = parent_context.security_context
    context.trace_context = parent_context.trace_context

    # Propagate parent context metadata
    context.metadata.update(parent_context.metadata)
    context.metadata["parent_context_id"] = parent_context.context_id

    # Meta-system specific optimization
    if meta_system_type:
        context.set_property("optimized_for", meta_system_type)

        if meta_system_type == "analyst":
            context.wave_particle_ratio = 0.3  # More particle-focused for analysis
        elif meta_system_type == "predictor":
            context.wave_particle_ratio = 0.7  # More wave-focused for exploration
        elif meta_system_type == "persona_core":
            context.wave_particle_ratio = 0.5  # Balanced for personality

    return context


def create_caw_optimized_context_legacy(
    domain: str, meta_system_type: Optional[str] = None, operation_type: Optional[str] = None
) -> UnifiedContext:
    """
    DEPRECATED: Create a CAW-optimized context without parent context.

    This function is deprecated and violates Principle II (Contextual Supremacy).
    Use create_caw_optimized_context() with a parent_context parameter instead.

    Args:
        domain: Domain for the new context
        meta_system_type: Optional meta-system type for optimization
        operation_type: Optional operation type for optimization

    Returns:
        UnifiedContext: New context (without proper context propagation)
    """
    import warnings
    warnings.warn(
        "create_caw_optimized_context_legacy is deprecated. "
        "Use create_caw_optimized_context() with parent_context parameter.",
        DeprecationWarning,
        stacklevel=2
    )

    # Create a minimal default parent context for backward compatibility
    default_parent = UnifiedContext.create_default("legacy_compatibility")

    return create_caw_optimized_context(
        domain=domain,
        parent_context=default_parent,
        meta_system_type=meta_system_type,
        operation_type=operation_type
    )


def validate_caw_integration(
    actor_system: Optional[ActorSystem], context: UnifiedContext
) -> Dict[str, Any]:
    """Validate CAW integration completeness."""

    validation_results = {
        "actor_system_available": actor_system is not None,
        "unified_context_available": isinstance(context, UnifiedContext),
        "acf_enabled": hasattr(context, "acf_setting"),
        "wave_particle_duality": hasattr(context, "wave_particle_ratio"),
        "resource_management": hasattr(context, "resources"),
        "context_composition": hasattr(context, "compose"),
        "overall_caw_alignment": 0.0,
    }

    # Calculate overall alignment score
    alignment_factors = [
        validation_results["actor_system_available"],
        validation_results["unified_context_available"],
        validation_results["acf_enabled"],
        validation_results["wave_particle_duality"],
        validation_results["resource_management"],
        validation_results["context_composition"],
    ]

    validation_results["overall_caw_alignment"] = sum(alignment_factors) / len(alignment_factors)

    return validation_results
