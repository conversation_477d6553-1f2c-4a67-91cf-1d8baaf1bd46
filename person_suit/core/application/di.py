"""Dependency Injection Registration for Application Subsystem.

Registers application configuration, factory, and related services with the DI container.
Aligns with v0.3 unified paradigm and design documentation for modular, context-aware application orchestration.

References:
- docs/future/unified_paradigm/v0.3/unified_paradigm_v0.3.md
- docs/design/Context_Management_Design.md
"""

from ..infrastructure.dependency_injection import ServiceCollection
from ..context.unified import UnifiedContext
from .config import DefaultApplicationConfig
from .factory import create_application_config
from .interfaces.config import ApplicationConfig


def register_with_container(container: ServiceCollection, context: "UnifiedContext") -> None:
    """
    Register application subsystem components with the DI container.

    This implementation follows Principle II (Contextual Supremacy) by requiring
    context for all infrastructure operations, including DI registration.

    Args:
        container: The DI service collection/container.
        context: UnifiedContext for the registration operation (REQUIRED)

    Raises:
        ValueError: If context is None
    """
    if context is None:
        raise ValueError("context is required for DI registration (Principle II)")

    # Add context metadata for registration tracking
    registration_metadata = {
        "registration_context_id": context.context_id,
        "registration_domain": context.domain,
        "registration_timestamp": context.timestamp
    }
    # Register ApplicationConfig (singleton) with context metadata
    container.add_singleton(
        ApplicationConfig,
        DefaultApplicationConfig,
        metadata=registration_metadata
    )

    # Register application configuration factory (singleton) with context awareness
    def context_aware_factory(_):
        """Factory that includes context information in configuration creation."""
        config = create_application_config()
        # Add context information to the configuration if supported
        if hasattr(config, 'set_creation_context'):
            config.set_creation_context(context)
        return config

    container.add_singleton(
        create_application_config,
        implementation_factory=context_aware_factory,
        metadata=registration_metadata
    )



