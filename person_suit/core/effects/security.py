"""Security Effects for Centralized Permission and Capability Management.

File: person_suit/core/effects/security.py
Purpose: Centralize all security operations as declarative Effects following Principle III
Related Files:
- person_suit/core/effects/base.py: Base Effect classes
- person_suit/core/effects/interpreter.py: Effect execution
- person_suit/core/security/capabilities.py: Capability verification
- person_suit/shared/isolation/permissions.py: Legacy permission system (to be deprecated)
Dependencies: typing, dataclasses, enum
"""

from __future__ import annotations

import logging
from dataclasses import dataclass
from enum import Enum, auto
from typing import Any, Dict, List, Optional

from .base import Effect, EffectType
from ..security.capabilities import CapabilityToken, Permission, CapabilityScope

logger = logging.getLogger(__name__)


class SecurityOperationType(Enum):
    """Types of security operations that can be performed."""
    
    PERMISSION_CHECK = auto()
    CAPABILITY_VERIFICATION = auto()
    ACCESS_CONTROL = auto()
    AUTHENTICATION = auto()
    AUTHORIZATION = auto()
    AUDIT_LOG = auto()


@dataclass
class PermissionCheckEffect(Effect):
    """
    Effect for checking permissions between systems.
    
    This replaces the scattered permission checks in shared/isolation/permissions.py
    by centralizing all permission logic in the EffectInterpreter.
    """
    
    source_system: str
    destination_system: str
    operation_type: str
    security_context: Optional[Dict[str, Any]] = None
    
    def __init__(
        self,
        source_system: str,
        destination_system: str,
        operation_type: str,
        security_context: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            effect_type=EffectType.SECURITY,
            operation="permission_check",
            required_capability=f"security:permission:check:{operation_type}",
            metadata=metadata,
        )
        self.source_system = source_system
        self.destination_system = destination_system
        self.operation_type = operation_type
        self.security_context = security_context or {}


@dataclass
class CapabilityVerificationEffect(Effect):
    """
    Effect for verifying capability tokens.
    
    This centralizes capability verification in the EffectInterpreter
    following Principle III (Capability as Sole Authority).
    """
    
    token: CapabilityToken
    required_permission: Permission
    required_scope: CapabilityScope
    verification_context: Optional[Dict[str, Any]] = None
    
    def __init__(
        self,
        token: CapabilityToken,
        required_permission: Permission,
        required_scope: CapabilityScope,
        verification_context: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            effect_type=EffectType.SECURITY,
            operation="capability_verification",
            required_capability=f"security:capability:verify:{required_permission.value}",
            metadata=metadata,
        )
        self.token = token
        self.required_permission = required_permission
        self.required_scope = required_scope
        self.verification_context = verification_context or {}


@dataclass
class AccessControlEffect(Effect):
    """
    Effect for access control decisions.
    
    This centralizes access control logic in the EffectInterpreter,
    replacing scattered authorization checks throughout the codebase.
    """
    
    subject_id: str
    resource_id: str
    action: str
    access_context: Optional[Dict[str, Any]] = None
    
    def __init__(
        self,
        subject_id: str,
        resource_id: str,
        action: str,
        access_context: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            effect_type=EffectType.SECURITY,
            operation="access_control",
            required_capability=f"security:access:control:{action}",
            metadata=metadata,
        )
        self.subject_id = subject_id
        self.resource_id = resource_id
        self.action = action
        self.access_context = access_context or {}


@dataclass
class SecurityAuditEffect(Effect):
    """
    Effect for security audit logging.
    
    This ensures all security operations are properly logged
    for compliance and monitoring purposes.
    """
    
    event_type: str
    subject_id: Optional[str] = None
    resource_id: Optional[str] = None
    action: Optional[str] = None
    result: Optional[str] = None
    audit_context: Optional[Dict[str, Any]] = None
    
    def __init__(
        self,
        event_type: str,
        subject_id: Optional[str] = None,
        resource_id: Optional[str] = None,
        action: Optional[str] = None,
        result: Optional[str] = None,
        audit_context: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            effect_type=EffectType.SECURITY,
            operation="security_audit",
            required_capability="security:audit:log",
            metadata=metadata,
        )
        self.event_type = event_type
        self.subject_id = subject_id
        self.resource_id = resource_id
        self.action = action
        self.result = result
        self.audit_context = audit_context or {}


# Factory functions for creating security effects

def create_permission_check_effect(
    source_system: str,
    destination_system: str,
    operation_type: str,
    security_context: Optional[Dict[str, Any]] = None,
) -> PermissionCheckEffect:
    """Create a permission check effect."""
    return PermissionCheckEffect(
        source_system=source_system,
        destination_system=destination_system,
        operation_type=operation_type,
        security_context=security_context,
    )


def create_capability_verification_effect(
    token: CapabilityToken,
    required_permission: Permission,
    required_scope: CapabilityScope,
    verification_context: Optional[Dict[str, Any]] = None,
) -> CapabilityVerificationEffect:
    """Create a capability verification effect."""
    return CapabilityVerificationEffect(
        token=token,
        required_permission=required_permission,
        required_scope=required_scope,
        verification_context=verification_context,
    )


def create_access_control_effect(
    subject_id: str,
    resource_id: str,
    action: str,
    access_context: Optional[Dict[str, Any]] = None,
) -> AccessControlEffect:
    """Create an access control effect."""
    return AccessControlEffect(
        subject_id=subject_id,
        resource_id=resource_id,
        action=action,
        access_context=access_context,
    )


def create_security_audit_effect(
    event_type: str,
    subject_id: Optional[str] = None,
    resource_id: Optional[str] = None,
    action: Optional[str] = None,
    result: Optional[str] = None,
    audit_context: Optional[Dict[str, Any]] = None,
) -> SecurityAuditEffect:
    """Create a security audit effect."""
    return SecurityAuditEffect(
        event_type=event_type,
        subject_id=subject_id,
        resource_id=resource_id,
        action=action,
        result=result,
        audit_context=audit_context,
    )


__all__ = [
    "SecurityOperationType",
    "PermissionCheckEffect",
    "CapabilityVerificationEffect", 
    "AccessControlEffect",
    "SecurityAuditEffect",
    "create_permission_check_effect",
    "create_capability_verification_effect",
    "create_access_control_effect",
    "create_security_audit_effect",
]
