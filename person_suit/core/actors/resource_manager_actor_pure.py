"""
Person Suit - CAW-Compliant Resource Manager Actor

This module provides a resource manager actor that fully adheres to CAW principles:
- Returns Effects instead of performing I/O
- Respects UnifiedContext in all operations
- Uses wave-particle duality for resource tracking
- No direct state mutations or bus operations

Related Files:
- person_suit/core/actors/base.py: Actor base class
- person_suit/core/effects/base.py: Effect system
- person_suit/core/context/unified.py: Unified context

Dependencies:
- Python 3.8+
- No direct I/O dependencies (all through Effects)
"""

import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum, auto, IntEnum
from typing import Any, Dict, List, Optional, Set, Union
import torch
import torch.nn as nn

from person_suit.core.actors.base import Actor, StandardActorMessage
from person_suit.core.context.unified import UnifiedContext
from person_suit.core.effects.base import Effect, EffectType, CompositeEffect
from person_suit.core.effects.event import PublishEventEffect
from person_suit.core.effects.state_effects import StateReadEffect, StateUpdateEffect, StateDeleteEffect
from person_suit.core.constants.fixed_point_scale import (
    PRIO_CRITICAL, PRIO_HIGH, PRIO_NORMAL, PRIO_LOW, PRIO_DEFERRED,
    FIDELITY_MAX, FIDELITY_HIGH, FIDELITY_MED, FIDELITY_LOW, FIDELITY_MIN
)

# Resource event types
class ResourceEventType(str, Enum):
    """Types of resource events."""
    ALLOCATION_REQUESTED = "allocation_requested"
    ALLOCATION_SUCCESS = "allocation_success" 
    ALLOCATION_FAILED = "allocation_failed"
    ALLOCATION_RELEASED = "allocation_released"
    RESOURCE_SCALED = "resource_scaled"
    CAPACITY_UPDATED = "capacity_updated"
    RESOURCE_RESERVED = "resource_reserved"
    RESOURCE_UNRESERVED = "resource_unreserved"

# Priority enum for type safety
class Priority(IntEnum):
    """Priority levels using canonical constants."""
    DEFERRED = PRIO_DEFERRED
    LOW = PRIO_LOW
    NORMAL = PRIO_NORMAL
    HIGH = PRIO_HIGH
    CRITICAL = PRIO_CRITICAL

logger = logging.getLogger(__name__)


# --- Resource-specific Effects ---

@dataclass
class AllocateResourceEffect(Effect):
    """Effect to allocate computational resources."""
    resource_type: str
    amount: float
    requestor_id: str
    priority: Priority
    duration_seconds: Optional[float] = None
    
    def __init__(self, resource_type: str, amount: float, requestor_id: str, 
                 priority: Priority = Priority.NORMAL, duration_seconds: Optional[float] = None, **kwargs):
        super().__init__(effect_type=EffectType.IO, operation="allocate_resource")  # Use IO type
        self.resource_type = resource_type
        self.amount = amount
        self.requestor_id = requestor_id
        self.priority = priority
        self.duration_seconds = duration_seconds
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "resource_type": self.resource_type,
            "amount": self.amount,
            "requestor_id": self.requestor_id,
            "priority": int(self.priority),
            "duration_seconds": self.duration_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AllocateResourceEffect':
        return cls(**data)

@dataclass
class PreemptAllocationsEffect(Effect):
    """Effect to preempt lower priority allocations."""
    resource_type: str
    needed_amount: float
    max_priority_to_preempt: Priority
    
    def __init__(self, resource_type: str, needed_amount: float, 
                 max_priority_to_preempt: Priority = Priority.LOW, **kwargs):
        super().__init__(effect_type=EffectType.IO, operation="preempt")
        self.resource_type = resource_type
        self.needed_amount = needed_amount
        self.max_priority_to_preempt = max_priority_to_preempt
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "resource_type": self.resource_type,
            "needed_amount": self.needed_amount,
            "max_priority_to_preempt": int(self.max_priority_to_preempt)
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PreemptAllocationsEffect':
        return cls(**data)

@dataclass
class UpdateResourceStateEffect(Effect):
    """Effect to update resource manager state based on utilization."""
    new_state: str
    max_utilization: float
    
    def __init__(self, new_state: str, max_utilization: float, **kwargs):
        super().__init__(effect_type=EffectType.STATE, operation="update_resource_state")
        self.new_state = new_state
        self.max_utilization = max_utilization


# --- Pure Data Classes (No I/O) ---

class ResourceState(Enum):
    """States for the resource manager."""
    INITIALIZING = auto()
    RUNNING = auto()
    CONSTRAINED = auto()
    CRITICAL = auto()
    SHUTTING_DOWN = auto()
    SHUTDOWN = auto()


@dataclass
class ResourceAllocation:
    """Immutable resource allocation record."""
    allocation_id: str
    resource_type: str
    amount: float
    requestor_id: str
    priority: Priority
    allocated_at: datetime
    expires_at: Optional[datetime] = None
    
    @property
    def is_expired(self) -> bool:
        """Check if allocation has expired."""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at


@dataclass 
class ResourcePool:
    """Immutable resource pool state."""
    resource_type: str
    total_capacity: float
    allocated_amount: float = 0.0
    reserved_amount: float = 0.0
    
    # Wave-particle duality tracking
    particle_allocations: Dict[str, float] = field(default_factory=dict)
    wave_metrics: Dict[str, float] = field(default_factory=lambda: {
        'mean_allocation': 0.0,
        'allocation_variance': 0.0,
        'pressure_score': 0.0,
        'allocation_rate': 0.0,
        'release_rate': 0.0
    })
    
    @property
    def available_amount(self) -> float:
        """Calculate available resources."""
        return self.total_capacity - self.allocated_amount - self.reserved_amount
    
    @property
    def utilization_ratio(self) -> float:
        """Calculate utilization ratio."""
        if self.total_capacity == 0:
            return 1.0
        return (self.allocated_amount + self.reserved_amount) / self.total_capacity


# --- CAW-Compliant Resource Manager Actor ---

class PureResourceManagerActor(Actor):
    """
    Pure resource manager that follows all architectural principles.
    
    Key differences from the original:
    - Returns Effects from receive() instead of performing operations
    - No direct state mutations - all state changes are Effects
    - No direct bus operations - all communication through Effects
    - Uses differentiable parameters for adaptive thresholds
    - Implements wave-particle duality through differential updates
    """
    
    def __init__(self):
        super().__init__()
        
        # Differentiable parameters (Principle IV)
        self.utilization_threshold = nn.Parameter(torch.tensor(0.8))
        self.critical_threshold = nn.Parameter(torch.tensor(0.95))
        self.preemption_threshold = nn.Parameter(torch.tensor(0.9))
        
        # Optimizer for learning thresholds
        self.optimizer = torch.optim.Adam([
            self.utilization_threshold,
            self.critical_threshold,
            self.preemption_threshold
        ], lr=0.001)
        
        logger.info("PureResourceManagerActor initialized with differentiable thresholds")
    
    async def receive(self, message: StandardActorMessage) -> Optional[Union[Effect, List[Effect]]]:
        """
        Pure message handler that returns Effects.
        
        This method contains NO side effects, NO I/O, and NO state mutations.
        It only analyzes the message and returns a description of what should happen.
        """
        channel = getattr(message, 'channel', '')
        command = channel.split('.')[-1]
        payload = getattr(message, 'payload', {})
        context = getattr(message, 'context', None)
        
        # Route to appropriate pure handler
        handler_map = {
            'allocate': self._handle_allocate,
            'release': self._handle_release, 
            'scale': self._handle_scale,
            'get_status': self._handle_get_status,
            'cleanup_expired': self._handle_cleanup_expired,
            'update_capacity': self._handle_update_capacity,
            'reserve': self._handle_reserve,
            'unreserve': self._handle_unreserve,
            'feedback': self._handle_feedback,  # For learning
        }
        
        handler = handler_map.get(command)
        if not handler:
            logger.warning(f"Unknown resource command: {command}")
            return None
        
        # All handlers return Effects
        return await handler(payload, context)
    
    async def _handle_allocate(self, payload: Dict, context: Optional[UnifiedContext]) -> Optional[List[Effect]]:
        """
        Handle allocation request by returning Effects.
        
        This is a pure function that analyzes the request and returns
        a list of Effects describing what should happen.
        """
        resource_type = payload.get('resource_type', '')
        amount = payload.get('amount', 0.0)
        requestor_id = payload.get('requestor_id', '')
        duration = payload.get('duration_seconds')
        
        if not all([resource_type, requestor_id, amount > 0]):
            return None
        
        effects = []
        
        # First, read current state to make decision
        state_key = f"resource_pool:{resource_type}"
        effects.append(
            StateReadEffect(key=state_key)
        )
        
        # The actual allocation logic will happen in the callback
        # after we receive the current state. For now, we declare
        # the intent to check and potentially allocate.
        
        priority_int = context.priority if context else PRIO_NORMAL
        priority = Priority(priority_int)
        
        # Declare adaptive behavior based on context (Principle II)
        if context and context.priority >= Priority.HIGH:
            # High priority might trigger preemption
            effects.append(
                PreemptAllocationsEffect(
                    resource_type=resource_type,
                    needed_amount=amount,
                    max_priority_to_preempt=Priority.NORMAL
                )
            )
        
        # Declare the allocation effect
        effects.append(
            AllocateResourceEffect(
                resource_type=resource_type,
                amount=amount,
                requestor_id=requestor_id,
                priority=priority,
                duration_seconds=duration
            )
        )
        
        # Declare event publication
        effects.append(
            PublishEventEffect(
                channel="event.resource.allocation_requested",
                payload={
                    "resource_type": resource_type,
                    "amount": amount,
                    "requestor_id": requestor_id,
                    "priority": int(priority) if priority else PRIO_NORMAL
                }
            )
        )
        
        return effects
    
    async def _handle_release(self, payload: Dict, context: Optional[UnifiedContext]) -> Optional[Effect]:
        """Handle release request by returning Effects."""
        allocation_id = payload.get('allocation_id')
        if not allocation_id:
            return None
        
        # Return effect to delete the allocation from state
        return CompositeEffect([
            StateDeleteEffect(key=allocation_id),
            PublishEventEffect(
                channel="event.resource.allocation_released",
                payload={"allocation_id": allocation_id}
            )
        ])
    
    async def _handle_scale(self, payload: Dict, context: Optional[UnifiedContext]) -> Optional[List[Effect]]:
        """Handle scaling request by returning Effects."""
        resource_type = payload.get('resource_type')
        scale_factor = payload.get('scale_factor', 1.0)
        reason = payload.get('reason', 'Manual scaling')
        
        if not resource_type:
            return None
        
        effects = []
        
        # Context-aware scaling limits (Principle II)
        if context and context.priority <= Priority.LOW:
            # Limit scaling for low priority
            scale_factor = min(scale_factor, 1.2)
        
        # Read current state first
        effects.append(
            StateReadEffect(key=f"resource_pool:{resource_type}")
        )
        
        # The actual scaling will be done after reading state
        # For now, declare the intent
        effects.append(
            PublishEventEffect(
                channel="event.resource.scale_requested",
                payload={
                    "resource_type": resource_type,
                    "scale_factor": scale_factor,
                    "reason": reason
                }
            )
        )
        
        return effects
    
    async def _handle_get_status(self, payload: Dict, context: Optional[UnifiedContext]) -> Effect:
        """Return status by reading from state."""
        # This demonstrates reading all resource pools
        return CompositeEffect([
            StateReadEffect(key="resources:*"),  # Read all resource pools
            StateReadEffect(key="allocations:*")  # Read all allocations
        ])
    
    async def _handle_cleanup_expired(self, payload: Dict, context: Optional[UnifiedContext]) -> Effect:
        """Return effect to trigger cleanup of expired allocations."""
        # This would normally read all allocations and check expiry
        # For now, return a simple effect
        return PublishEventEffect(
            channel="command.internal.check_expired_allocations",
            payload={"timestamp": datetime.now().isoformat()}
        )
    
    async def _handle_update_capacity(self, payload: Dict, context: Optional[UnifiedContext]) -> Optional[List[Effect]]:
        """Update capacity by returning state update effect."""
        resource_type = payload.get('resource_type')
        new_capacity = payload.get('capacity')
        
        if not resource_type or new_capacity is None:
            return None
        
        return [
            StateUpdateEffect(
                key=f"resource_pool:{resource_type}:capacity",
                value={"capacity": float(new_capacity)}
            ),
            PublishEventEffect(
                channel="event.resource.capacity_updated",
                payload={
                    "resource_type": resource_type,
                    "new_capacity": new_capacity
                }
            )
        ]
    
    async def _handle_reserve(self, payload: Dict, context: Optional[UnifiedContext]) -> Optional[List[Effect]]:
        """Reserve resources by returning Effects."""
        resource_type = payload.get('resource_type')
        amount = payload.get('amount', 0.0)
        
        if not resource_type or amount <= 0:
            return None
        
        return [
            StateUpdateEffect(
                key=f"resource_pool:{resource_type}:reserved",
                value={"amount": amount, "timestamp": datetime.now().isoformat()}
            ),
            PublishEventEffect(
                channel="event.resource.reserved",
                payload={
                    "resource_type": resource_type,
                    "amount": amount
                }
            )
        ]
    
    async def _handle_unreserve(self, payload: Dict, context: Optional[UnifiedContext]) -> Optional[List[Effect]]:
        """Unreserve resources by returning Effects."""
        resource_type = payload.get('resource_type')
        amount = payload.get('amount', 0.0)
        
        if not resource_type or amount <= 0:
            return None
        
        return [
            StateDeleteEffect(
                key=f"resource_pool:{resource_type}:reserved"
            ),
            PublishEventEffect(
                channel="event.resource.unreserved",
                payload={
                    "resource_type": resource_type,
                    "amount": amount
                }
            )
        ]
    
    async def _handle_feedback(self, payload: Dict, context: Optional[UnifiedContext]) -> Optional[Effect]:
        """
        Handle feedback for learning (Principle IV: Differentiable by Design).
        
        This demonstrates how the actor can learn from operational feedback
        to adjust its thresholds.
        """
        feedback_type = payload.get('type')
        
        if feedback_type == 'false_preemption':
            # We preempted but it wasn't necessary
            actual_utilization = payload.get('actual_utilization', 0.8)
            
            # Calculate loss - we want to increase the preemption threshold
            target = torch.tensor(actual_utilization + 0.1)
            loss = (self.preemption_threshold - target).pow(2)
            
            # Update the parameter
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
            
            logger.info(f"Learned from feedback: new preemption threshold = {self.preemption_threshold.item()}")
            
            # Return effect to persist the new threshold
            return StateUpdateEffect(
                key="learned_thresholds",
                value={
                    "preemption_threshold": self.preemption_threshold.item(),
                    "utilization_threshold": self.utilization_threshold.item(),
                    "critical_threshold": self.critical_threshold.item()
                }
            )
        
        return None 