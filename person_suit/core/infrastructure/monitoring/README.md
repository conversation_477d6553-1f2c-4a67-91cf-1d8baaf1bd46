"""Person Suit - Monitoring Infrastructure

Purpose: System monitoring, metrics collection, and telemetry
Related Files: metrics.py, telemetry.py, service.py
Dependencies: psutil, asyncio, hybrid_message_bus
"""

# Monitoring Infrastructure

This directory contains the monitoring infrastructure for Person Suit, providing
system observability through metrics, telemetry, alerts, and diagnostics.

## Key Components

### Metrics vs Telemetry

**Metrics (`metrics.py`):**
- **Purpose**: Persistent, system-wide resource monitoring
- **Scope**: Hardware resources (CPU, memory, disk, network)
- **Storage**: Can be persisted to time-series databases
- **Implementation**: Uses `psutil` for real system data
- **Use Cases**:
  - System resource tracking
  - Performance baselines
  - Capacity planning
  - Historical analysis
- **Example**: CPU usage over the last hour

**Telemetry (`telemetry.py`):**
- **Purpose**: Ephemeral, in-process event tracking
- **Scope**: Application-level events and counters
- **Storage**: In-memory only (lost on restart)
- **Implementation**: Simple counter/gauge tracking
- **Use Cases**:
  - Request counting
  - Error rates
  - Operation timing
  - Debug tracing
- **Example**: Number of messages processed this session

### Core Services

1. **MonitoringService** (`service.py`)
   - Central coordination of all monitoring activities
   - Manages metrics collectors, alert manager, anomaly detection
   - Provides unified monitoring interface

2. **AlertManager** (`alert_manager.py`)
   - Threshold-based alerting with cooldown periods
   - Event-driven alert notifications
   - Configurable alert rules

3. **AnomalyDetector** (`anomaly.py`)
   - Statistical anomaly detection
   - Z-score based outlier detection
   - Adaptive thresholds

4. **DualWave Monitoring** (`dual_wave/`)
   - CAW-specific monitoring implementations
   - Wave-particle state tracking
   - Context-aware metrics adaptation

## Architecture

```
monitoring/
├── interfaces/          # Abstract interfaces for all components
├── dual_wave/          # CAW-specific implementations
├── metrics.py          # System metrics (persistent, hardware)
├── telemetry.py        # App telemetry (ephemeral, counters)
├── service.py          # Central monitoring service
├── alert_manager.py    # Alert management
├── anomaly.py          # Anomaly detection
└── performance.py      # Performance helpers (actor-compatible)
```

## Usage Examples

### Metrics (Persistent System Monitoring)
```python
from person_suit.core.infrastructure.monitoring.metrics import ActorCompatibleSystemMonitor

monitor = ActorCompatibleSystemMonitor()
system_stats = await monitor.collect_system_metrics()
# Returns: {"cpu_percent": 45.2, "memory_percent": 62.1, ...}
```

### Telemetry (Ephemeral Event Tracking)
```python
from person_suit.core.infrastructure.monitoring.telemetry import TelemetryService

telemetry = TelemetryService()
telemetry.increment_counter("messages_processed")
telemetry.record_value("processing_time_ms", 125.3)
stats = telemetry.get_all_metrics()
# Returns current session counters
```

## Migration Status

All monitoring components have been migrated from thread-based to actor-based
implementations. The old thread-based files have been removed or converted to
use asyncio and effects.

## Integration Points

- **HybridMessageBus**: All monitoring events are published via the bus
- **Effects System**: Monitoring operations use effects for side-effects
- **Actor System**: Monitoring components can run as supervised actors
- **Diagnostics**: Provides data for health checks and diagnostics

## Best Practices

1. Use **metrics** for:
   - System resource monitoring
   - Long-term trends
   - Capacity planning
   - SLA tracking

2. Use **telemetry** for:
   - Request/response tracking
   - Error counting
   - Performance profiling
   - Debug information

3. Always use the actor-compatible versions
4. Publish important events to the message bus
5. Configure appropriate alert thresholds
6. Enable anomaly detection for critical metrics 