"""Compatibility stub until Sprint-4 Security Enforcement.

This module only provides the *names* expected by pre-existing tests so that
Sprint-0→3 integration can run without pulling in the full security stack.

Actual adaptive-capability enforcement will arrive in Sprint-4 where this
placeholder will be replaced by a fully-featured implementation.
"""

from __future__ import annotations

import sys
import types
from enum import Enum
from enum import auto

from person_suit.core.context.unified import SecurityContext as _UC_SecurityContext

# Import AuthenticationManager from the actual module to avoid duplication
from person_suit.core.infrastructure.security.authentication import AuthenticationManager


class SecurityContext(_UC_SecurityContext):  # noqa: D101
    pass


__all__ = ["SecurityContext", "AuthenticationManager"]


# ---------------------------------------------------------------------------
# Dynamic stub for ``person_suit.core.infrastructure.security.authentication``
# and its ``auth_manager`` child expected by old deployment tests.
# ---------------------------------------------------------------------------

_auth_pkg_name = __name__ + ".authentication"
_auth_pkg = types.ModuleType(_auth_pkg_name)

# Use the imported AuthenticationManager instead of defining it again

_auth_manager_mod = types.ModuleType(_auth_pkg_name + ".auth_manager")
_auth_manager_mod.AuthenticationManager = AuthenticationManager

# Expose in sys.modules so that ``import ...authentication.auth_manager`` works.
sys.modules[_auth_pkg_name] = _auth_pkg
sys.modules[_auth_manager_mod.__name__] = _auth_manager_mod


# ---------------------------------------------------------------------------
# Enum placeholders expected by deployment layer (to be replaced in Sprint-4)
# ---------------------------------------------------------------------------


class AccessLevel(Enum):  # noqa: D101
    LOW = auto()
    MEDIUM = auto()
    HIGH = auto()


class SecurityRole(Enum):  # noqa: D101
    USER = auto()
    ADMIN = auto()
    SYSTEM = auto()


class TokenType(Enum):  # noqa: D101
    BEARER = auto()
    API_KEY = auto()
    SESSION = auto()


# TokenCredential is referred from auth_manager; expose here as well for import ease.


class TokenCredential:  # noqa: D101
    token: str

    def __init__(self, token: str = "stub-token") -> None:  # noqa: D401, ANN001
        self.token = token

    def __repr__(self) -> str:  # noqa: D401
        return f"<TokenCredential {self.token}>"


_auth_manager_mod.TokenCredential = TokenCredential  # type: ignore[attr-defined]

# Re-export for `from ...infrastructure.security import TokenCredential, ...`
__all__.extend(["AccessLevel", "SecurityRole", "TokenType", "TokenCredential"])
