"""
Operations for CAW Integration
===========================

This module provides operations for integrating differential dataflow with
the CAW paradigm. It enables context-sensitive differential dataflow using
CAW components.

Related Files:
- ../../core.py: Core types and classes
- ../../collections/base.py: Base collection implementations
- ../../../wave/core.py: Wave-based information representation
- ../../../caw/core.py: CAW components

Dependencies:
- Python 3.8+
- NumPy
"""

import logging
from typing import Any
from typing import Dict
from typing import Optional
from typing import TypeVar
from typing import Union
from typing import cast

# Core infrastructure imports
from person_suit.core.context.unified import UnifiedContext
from person_suit.core.infrastructure.differential.core import DifferentialCollection
from person_suit.core.wave.information import DualInformation as Information
from person_suit.core.wave.wave_function import DualWaveFunction

# Effects system
# Local imports
from .caw_differential import CAWDifferential
from .caw_differential import WaveTransformation
from .context_sensitive_collection import CAWProcessor
from .context_sensitive_collection import EnvironmentSensitiveDifferentialCollection

# Configure logging
logger = logging.getLogger(__name__)

# Type variables for generic types
K = TypeVar("K")
V = TypeVar("V")
R = TypeVar("R")

# Registry of differential wave functions
_differential_wave_functions: Dict[str, DualWaveFunction] = {}


def register_differential_wave_function(name: str, wave_function: DualWaveFunction) -> None:
    """
    Register a differential wave function.

    Args:
        name: The name of the wave function
        wave_function: The wave function to register
    """
    _differential_wave_functions[name] = wave_function


def get_differential_wave_function(name: str) -> Optional[DualWaveFunction]:
    """
    Get a registered differential wave function.

    Args:
        name: The name of the wave function

    Returns:
        The wave function, or None if not found
    """
    return _differential_wave_functions.get(name)


def process_in_context(
    collection: Union[DifferentialCollection, Dict[K, V]],
    context: UnifiedContext,
    wave_function: Optional[Union[str, DualWaveFunction]] = None,
    processor: Optional[CAWProcessor] = None,
) -> Dict[K, V]:
    """
    Process a collection in a specific context.

    Args:
        collection: The collection to process
        context: The context in which to process
        wave_function: Optional wave function or name of a registered wave function
        processor: Optional CAW processor to use

    Returns:
        Dictionary of processed values
    """
    # Get the wave function
    wave_func = wave_function
    if isinstance(wave_func, str):
        wave_func = get_differential_wave_function(wave_func)
        if wave_func is None:
            raise ValueError(f"Differential wave function not found: {wave_function}")

    # Create a CAW processor if not provided
    proc = processor or CAWProcessor()

    # Process the collection
    if isinstance(collection, CAWDifferential):
        # Use the CAW differential's process_all_in_context method
        return cast(Dict[K, V], collection.process_all_in_context(context, proc))
    elif isinstance(collection, EnvironmentSensitiveDifferentialCollection):
        # Process all values in the context
        processed_values = {}

        for key, value in collection.items_in_context(context):
            # Create information with the wave function
            if wave_func is not None:
                information = Information(value, wave_func)

                # Process the information using the CAW processor
                processed_info = proc.process(information, context)

                # Extract the processed value
                processed_values[key] = processed_info.value
            else:
                processed_values[key] = value

        return processed_values
    elif isinstance(collection, DifferentialCollection):
        # Process all values in the collection
        processed_values = {}

        for key, value in collection:
            # Create information with the wave function
            if wave_func is not None:
                information = Information(value, wave_func)

                # Process the information using the CAW processor
                processed_info = proc.process(information, context)

                # Extract the processed value
                processed_values[key] = processed_info.value
            else:
                processed_values[key] = value

        return processed_values
    else:
        # Process all values in the dictionary
        processed_values = {}

        for key, value in collection.items():
            # Create information with the wave function
            if wave_func is not None:
                information = Information(value, wave_func)

                # Process the information using the CAW processor
                processed_info = proc.process(information, context)

                # Extract the processed value
                processed_values[key] = processed_info.value
            else:
                processed_values[key] = value

        return processed_values


def transform_in_context(
    collection: Union[DifferentialCollection, Dict[K, V]],
    context: UnifiedContext,
    transformation: WaveTransformation,
    wave_function: Optional[Union[str, DualWaveFunction]] = None,
    processor: Optional[CAWProcessor] = None,
) -> Dict[K, V]:
    """
    Transform a collection in a specific context.

    Args:
        collection: The collection to transform
        context: The context in which to transform
        transformation: The transformation to apply
        wave_function: Optional wave function or name of a registered wave function
        processor: Optional CAW processor to use

    Returns:
        Dictionary of transformed values
    """
    # Get the wave function
    wave_func = wave_function
    if isinstance(wave_func, str):
        wave_func = get_differential_wave_function(wave_func)
        if wave_func is None:
            raise ValueError(f"Differential wave function not found: {wave_function}")

    # Create a CAW processor if not provided
    proc = processor or CAWProcessor()

    # Transform the collection
    if isinstance(collection, CAWDifferential):
        # Use the CAW differential's transform_all_in_context method
        return cast(Dict[K, V], collection.transform_all_in_context(context, transformation, proc))
    elif isinstance(collection, EnvironmentSensitiveDifferentialCollection):
        # Transform all values in the context
        transformed_values = {}

        for key, value in collection.items_in_context(context):
            # Create information with the wave function
            if wave_func is not None:
                information = Information(value, wave_func)

                # Transform the information using the transformation
                transformed_info = transformation.apply(information, context)

                # Process the transformed information using the CAW processor
                processed_info = proc.process(transformed_info, context)

                # Extract the processed value
                transformed_values[key] = processed_info.value
            else:
                # If no wave function, apply transformation directly
                transformed_values[key] = transformation.apply(value, context)

        return transformed_values
    elif isinstance(collection, DifferentialCollection):
        # Transform all values in the collection
        transformed_values = {}

        for key, value in collection:
            # Create information with the wave function
            if wave_func is not None:
                information = Information(value, wave_func)

                # Transform the information using the transformation
                transformed_info = transformation.apply(information, context)

                # Process the transformed information using the CAW processor
                processed_info = proc.process(transformed_info, context)

                # Extract the processed value
                transformed_values[key] = processed_info.value
            else:
                # If no wave function, apply transformation directly
                transformed_values[key] = transformation.apply(value, context)

        return transformed_values
    else:
        # Transform all values in the dictionary
        transformed_values = {}

        for key, value in collection.items():
            # Create information with the wave function
            if wave_func is not None:
                information = Information(value, wave_func)

                # Transform the information using the transformation
                transformed_info = transformation.apply(information, context)

                # Process the transformed information using the CAW processor
                processed_info = proc.process(transformed_info, context)

                # Extract the processed value
                transformed_values[key] = processed_info.value
            else:
                # If no wave function, apply transformation directly
                transformed_values[key] = transformation.apply(value, context)

        return transformed_values
