"""
Differential Dataflow Engine
==========================

This module implements the differential dataflow engine, which manages
differential collections, operators, and dependencies.

Related Files:
- core.py: Core types and classes
- collections/base.py: Base collection implementations
- operators/__init__.py: Basic operators

Dependencies:
- Python 3.8+
"""

import logging
import time
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional
from typing import Set
from typing import TypeVar

from .core import DifferentialCollection
from .core import DifferentialOperator
from .core import Version

# Configure logging
logger = logging.getLogger(__name__)

# Type variables for generic types
T = TypeVar("T")
K = TypeVar("K")
V = TypeVar("V")


class DifferentialEngine:
    """
    Differential dataflow engine.

    The engine manages differential collections, operators, and dependencies,
    and ensures that changes are properly propagated through the computation
    graph.

    Attributes:
        collections: Registered collections
        operators: Registered operators
        dependencies: Dependencies between collections
        dependents: Collections that depend on each collection
        is_initialized: Whether the engine has been initialized
    """

    _instance = None

    def __new__(cls) -> "DifferentialEngine":
        """Implement the singleton pattern."""
        if cls._instance is None:
            cls._instance = super(DifferentialEngine, cls).__new__(cls)
            cls._instance.is_initialized = False
        return cls._instance

    def __init__(self) -> None:
        """Initialize the differential engine."""
        if self.is_initialized:
            return

        self.collections: Dict[str, DifferentialCollection] = {}
        self.operators: Dict[str, DifferentialOperator] = {}
        self.dependencies: Dict[str, Set[str]] = {}
        self.dependents: Dict[str, Set[str]] = {}
        self.is_initialized = True

        logger.info("Initialized differential engine")

    async def initialize(self) -> None:
        """Initialize the engine asynchronously."""
        if self.is_initialized:
            return

        self.collections = {}
        self.operators = {}
        self.dependencies = {}
        self.dependents = {}
        self.is_initialized = True

        logger.info("Asynchronously initialized differential engine")

    def register_collection(self, name: str, collection: DifferentialCollection) -> None:
        """
        Register a collection with the engine.

        Args:
            name: Name of the collection
            collection: The collection to register
        """
        if name in self.collections:
            logger.warning(f"Collection {name} already registered, replacing")

        self.collections[name] = collection
        self.dependencies[name] = set()
        self.dependents[name] = set()

        logger.info(f"Registered collection {name} with {len(collection)} items")

    def register_operator(
        self,
        name: str,
        operator: DifferentialOperator,
        input_collections: List[str],
        output_collection: str,
    ) -> None:
        """
        Register an operator with the engine.

        Args:
            name: Name of the operator
            operator: The operator to register
            input_collections: Names of input collections
            output_collection: Name of output collection
        """
        if name in self.operators:
            logger.warning(f"Operator {name} already registered, replacing")

        self.operators[name] = operator

        # Register dependencies
        for input_name in input_collections:
            if input_name not in self.collections:
                raise ValueError(f"Input collection {input_name} not registered")

            self.dependencies[output_collection].add(input_name)
            self.dependents[input_name].add(output_collection)

            # Register change handler
            self.collections[input_name].on_change(
                lambda from_v,
                to_v,
                input=input_name,
                output=output_collection: self._propagate_changes(input, output, from_v, to_v)
            )

        logger.info(
            f"Registered operator {name} with inputs {input_collections} and output {output_collection}"
        )

    @effect_decorator_effects([Computation])
    def _propagate_changes(
        self,
        input_collection: str,
        output_collection: str,
        from_version: Version,
        to_version: Version,
    ) -> None:
        """
        Propagate changes from an input collection to an output collection.

        Args:
            input_collection: Name of the input collection
            output_collection: Name of the output collection
            from_version: Starting version
            to_version: Ending version
        """
        # Find the operator that produces the output collection
        operator_name = None
        for name, op in self.operators.items():
            if (
                output_collection in self.dependencies
                and input_collection in self.dependencies[output_collection]
            ):
                operator_name = name
                break

        if operator_name is None:
            logger.warning(
                f"No operator found for propagating changes from {input_collection} to {output_collection}"
            )
            return

        # Get the changes
        self.collections[input_collection].compute_diff(from_version, to_version)

        # Apply the operator to the changes
        # This is a simplified implementation; in a real system, we would
        # apply the operator only to the changed data
        self.operators[operator_name]
        # result = operator.apply(input_diff)

        # Update the output collection
        # self.collections[output_collection].batch_update(result)

        logger.debug(f"Propagated changes from {input_collection} to {output_collection}")

    @effect_decorator_effects([Computation, StateRead])
    def get_collection(self, name: str) -> Optional[DifferentialCollection]:
        """
        Get a collection by name.

        Args:
            name: Name of the collection

        Returns:
            The collection or None if not found
        """
        return self.collections.get(name)

    @effect_decorator_effects([Computation, StateRead])
    def get_dependencies(self, name: str) -> Set[str]:
        """
        Get the dependencies of a collection.

        Args:
            name: Name of the collection

        Returns:
            Set of dependency names
        """
        return self.dependencies.get(name, set())

    @effect_decorator_effects([Computation, StateRead])
    def get_dependents(self, name: str) -> Set[str]:
        """
        Get the collections that depend on a collection.

        Args:
            name: Name of the collection

        Returns:
            Set of dependent collection names
        """
        return self.dependents.get(name, set())

    @effect_decorator_effects([Computation])
    def execute_query(
        self, query: Callable[..., DifferentialCollection], *args: Any, **kwargs: Any
    ) -> DifferentialCollection:
        """
        Execute a query against the engine.

        Args:
            query: Query function that returns a differential collection
            *args: Arguments to pass to the query
            **kwargs: Keyword arguments to pass to the query

        Returns:
            The result collection
        """
        start_time = time.time()
        result = query(*args, **kwargs)
        end_time = time.time()

        logger.info(f"Executed query in {end_time - start_time:.6f} seconds")
        return result


# Singleton instance
_engine_instance = None


def get_differential_engine() -> DifferentialEngine:
    """
    Get the singleton instance of the differential engine.

    Returns:
        The differential engine instance
    """
    global _engine_instance
    if _engine_instance is None:
        _engine_instance = DifferentialEngine()
    return _engine_instance
