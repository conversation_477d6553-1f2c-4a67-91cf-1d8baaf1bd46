#!/usr/bin/env python3
"""person_suit.core.infrastructure.message_based_core

Core message-based infrastructure for Person Suit.

This module provides the primary message-based communication patterns
that replace direct service imports throughout the system.

All components should use message-based communication through the
HybridMessageBus instead of direct imports and instantiation.
"""

from __future__ import annotations

from typing import Any, Dict, Optional

from .hybrid_message_bus import get_message_bus
from .hybrid_message import create_message, HybridMessage, MessageType
from ..context.unified import UnifiedContext


async def send_service_request(
    service_name: str,
    operation: str,
    context: UnifiedContext,
    payload: Optional[Dict[str, Any]] = None,
    **kwargs
) -> Any:
    """
    Send a service request through the message bus instead of direct import.

    This replaces direct service imports with message-based communication.

    Args:
        service_name: Name of the service to call
        operation: Operation to perform
        context: UnifiedContext for the request
        payload: Request payload
        **kwargs: Additional parameters

    Returns:
        Service response
    """
    bus = get_message_bus()

    # Create service request message
    message = create_message(
        channel=f"service.{service_name}",
        context=context,
        message_type=MessageType.COMMAND,
        payload={
            "operation": operation,
            "parameters": payload or {},
            **kwargs
        }
    )

    # Send request and wait for response
    response = await bus.send_and_wait(message)
    return response


async def get_service_proxy(service_name: str, context: UnifiedContext) -> "ServiceProxy":
    """
    Get a message-based proxy for a service instead of direct import.

    Args:
        service_name: Name of the service
        context: UnifiedContext for the proxy

    Returns:
        ServiceProxy that communicates via messages
    """
    return ServiceProxy(service_name, context)


class ServiceProxy:
    """
    Proxy that provides service access through message-based communication.

    This replaces direct service imports and instantiation.
    """

    def __init__(self, service_name: str, context: UnifiedContext):
        self.service_name = service_name
        self.context = context
        self.bus = get_message_bus()

    async def call(self, operation: str, **kwargs) -> Any:
        """Call a service operation through the message bus."""
        return await send_service_request(
            self.service_name,
            operation,
            self.context,
            kwargs
        )

    def __getattr__(self, name: str):
        """Dynamic method creation for service operations."""
        async def service_method(**kwargs):
            return await self.call(name, **kwargs)
        return service_method

__all__: list[str] = [
    "send_service_request",
    "get_service_proxy",
    "ServiceProxy",
    "create_message",
    "HybridMessage",
    "MessageType",
    "get_message_bus",
]
