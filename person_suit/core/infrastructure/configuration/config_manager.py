import asyncio
import json
import logging
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Union
from dataclasses import dataclass, asdict

import yaml

from person_suit.core.actors.base import Actor, StandardActorMessage
from person_suit.core.effects.base import Effect, EffectType, parallel
from person_suit.core.effects.event import PublishEventEffect
from person_suit.core.effects.io_effects import ReadFileEffect
from person_suit.core.infrastructure.configuration.schema.validator import get_validator
from person_suit.core.infrastructure.configuration.environment.handler import get_environment_handler
from person_suit.core.infrastructure.configuration.provider.provider import get_config_provider
from person_suit.core.infrastructure.hybrid_message import HybridMessage, MessageType

logger = logging.getLogger(__name__)

# --- Custom Effects and Events ---

@dataclass
class ConfigChangedEventData:
    section: str
    key: Optional[str]
    old_value: Any
    new_value: Any

# --- Actor Implementation ---

class ConfigManagerActor(Actor):
    """Actor-based central manager for configuration handling."""

    def __init__(self):
        super().__init__()
        self._validator = get_validator()
        self._environment_handler = get_environment_handler()
        self._provider = get_config_provider()
        self._config: Dict[str, Any] = {}
        self._defaults: Dict[str, Any] = {}
        self._schemas: Dict[str, Any] = {}
        logger.info("ConfigManagerActor initialized")

    async def pre_start(self):
        """Subscribe to commands and trigger initial load."""
        logger.info(f"ConfigManagerActor {self.path} starting.")
        # In a real system, the actor system would inject the bus or a bus provider.
        # For now, we assume it can be accessed.
        try:
            bus = self.context.actor_system.bus
            await bus.subscribe("command.config.*", self.receive, subscriber_id=str(self.path))
            await self.self_ref.tell("load", {"config_paths": ["config/config.yaml"], "env_prefix": "PERSON_SUIT"})
        except Exception as e:
            logger.error(f"ConfigManagerActor failed to subscribe to bus: {e}")

    async def receive(self, message: StandardActorMessage):
        """Handle incoming configuration commands."""
        command = message.type.split('.')[-1]
        payload = message.payload or {}
        sender = message.sender_ref

        handler = getattr(self, f"_handle_{command}", self._handle_unknown)
        result = await handler(payload)

        if sender and command in ["get", "export"]:
            # Send result back to asker
            await sender.tell(f"{message.type}.response", result, sender=self.self_ref)

    async def _handle_unknown(self, payload: Dict):
        logger.warning(f"Unknown config command: {payload}")
        return None

    async def _dispatch_effect(self, effect: Effect):
        try:
            bus = self.context.actor_system.bus
            msg = HybridMessage(
                channel="command.effects.execute",
                message_type=MessageType.COMMAND,
                payload={"effect": effect.to_dict(), "context": self.current_message_context.to_dict() if self.current_message_context else None}
            )
            await bus.send(msg)
        except Exception as e:
            logger.error(f"Failed to dispatch effect on bus: {e}")

    async def _handle_load(self, payload: Dict) -> None:
        """Handles loading configuration from files and environment."""
        config_paths = payload.get("config_paths", [])
        env_prefix = payload.get("env_prefix", "PERSON_SUIT")

        await self._environment_handler.initialize(env_prefix)

        for path in config_paths:
            config_path = Path(path)
            if config_path.exists():
                try:
                    data = yaml.safe_load(config_path.read_text())
                    self._merge_configuration(data)
                except Exception as e:
                    logger.error(f"Failed to load config file {path}: {e}")

        env_overrides = await self._environment_handler.get_environment_overrides()
        for section, key, value in env_overrides:
            if section not in self._config: self._config[section] = {}
            if key:
                self._config[section][key] = value
            else:
                self._config[section] = value

        logger.info("Configuration loaded and environment overrides applied.")

    def _merge_configuration(self, new_data: Dict[str, Any]):
        for section, values in new_data.items():
            if section in self._config and isinstance(self._config[section], dict) and isinstance(values, dict):
                self._config[section].update(values)
            else:
                self._config[section] = values

    async def _handle_get(self, payload: Dict) -> Any:
        section = payload.get("section")
        key = payload.get("key")
        default = payload.get("default")
        
        if section is None:
            return self._config
        
        conf_section = self._config.get(section, self._defaults.get(section, {}))
        
        if key is None:
            return conf_section

        if isinstance(conf_section, dict):
            return conf_section.get(key, default)
        return default

    async def _handle_update(self, payload: Dict) -> bool:
        section = payload["section"]
        key = payload["key"]
        value = payload["value"]
        
        old_value = self._config.get(section, {}).get(key)
        
        if section not in self._config:
            self._config[section] = {}
        self._config[section][key] = value

        if section in self._schemas:
            errors = self._validator.validate(section, self._config[section])
            if errors:
                logger.error(f"Config update failed validation: {errors}")
                self._config[section][key] = old_value # Revert
                return False
        
        event_payload = ConfigChangedEventData(section, key, old_value, value)
        event_effect = PublishEventEffect(channel="event.config.changed", payload=asdict(event_payload))
        await self._dispatch_effect(event_effect)
        return True

__all__ = ["ConfigManagerActor", "ConfigChangedEventData"] 