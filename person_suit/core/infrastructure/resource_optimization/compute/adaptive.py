# Message-based services initialization


async def _ensure_message_services():
    """Ensure message-based services are initialized."""
        raise RuntimeError("Failed to initialize message-based services")


"""
Person Suit - Adaptive Resource Scaling

This module provides dynamic resource scaling capabilities for the Person Suit framework.
It enables automatic adjustment of compute resources based on workload demands, system
load, and performance metrics, with specialized optimizations for Apple M3 Max hardware.

Components:
- AdaptiveScaler: Main component for adaptive resource scaling
- ScalingTrigger: Determines when to scale resources based on metrics
- ScalingAction: Defines the scaling action to take
- ScalingPolicy: Strategy for determining how to scale resources

Related Files:
- allocator.py: Core compute resource allocation
- scheduler.py: Task scheduling system
- policy.py: Resource allocation policies

Dependencies:
- person_suit.core.infrastructure.monitoring: For resource usage metrics
- person_suit.core.infrastructure.resource_optimization.compute.allocator: For resource allocation
"""

import logging
import asyncio
import time
from abc import ABC
from abc import abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional

from ...monitoring.health import HealthStatus

# Correctly import monitoring components directly
# Import compute resource allocator
from .allocator import get_compute_allocator

# Setup logging
logger = logging.getLogger(__name__)

# ------------------------------------------------------------------------------
# Scaling Types and Data Classes
# ------------------------------------------------------------------------------


class ScalingDirection(Enum):
    """Direction of resource scaling."""

    UP = "up"  # Scale resources up
    DOWN = "down"  # Scale resources down
    NONE = "none"  # No scaling needed


class ResourceType(Enum):
    """Types of resources that can be scaled."""

    CPU = "cpu"  # CPU cores
    MEMORY = "memory"  # Memory allocation
    THREADS = "threads"  # Async pool size
    PROCESSES = "processes"  # Process count


@dataclass
class ScalingTrigger:
    """Represents a trigger for resource scaling."""

    resource_type: ResourceType  # Resource type to scale
    direction: ScalingDirection  # Scaling direction
    reason: str  # Reason for scaling
    severity: float = 1.0  # Severity/urgency (0.0-1.0)
    metric_value: Optional[float] = None  # Value that triggered scaling
    threshold: Optional[float] = None  # Threshold that was crossed

    @property
    def is_actionable(self) -> bool:
        """Whether this trigger requires action."""
        return self.direction != ScalingDirection.NONE


@dataclass
class ScalingAction:
    """Represents a resource scaling action to take."""

    resource_type: ResourceType  # Resource type to scale
    direction: ScalingDirection  # Scaling direction
    magnitude: float  # Scaling magnitude (factor or absolute)
    reason: str  # Reason for scaling
    is_percentage: bool = True  # Whether magnitude is a percentage
    target_id: Optional[str] = None  # Target resource ID to scale
    max_value: Optional[int] = None  # Maximum value after scaling
    min_value: Optional[int] = None  # Minimum value after scaling


@dataclass
class ScalingResult:
    """Result of a scaling action."""

    action: ScalingAction  # Action that was taken
    success: bool  # Whether scaling was successful
    message: str  # Result message
    old_allocation: Optional[Any] = None  # Previous allocation
    new_allocation: Optional[Any] = None  # New allocation
    execution_time: float = 0.0  # Time taken to execute action


# ------------------------------------------------------------------------------
# Scaling Policy Interface
# ------------------------------------------------------------------------------


class ScalingPolicy(ABC):
    """Base class for scaling policies."""

    @abstractmethod
    def evaluate_metrics(self, metrics: Dict[str, Any]) -> List[ScalingTrigger]:
        """
        Evaluate metrics to determine if scaling is needed.

        Args:
            metrics: Dictionary of metrics to evaluate

        Returns:
            List of scaling triggers
        """
        pass

    @abstractmethod
    def create_scaling_action(self, trigger: ScalingTrigger) -> ScalingAction:
        """
        Create a scaling action based on a trigger.

        Args:
            trigger: Scaling trigger

        Returns:
            Scaling action to take
        """
        pass


# ------------------------------------------------------------------------------
# Load-Based Scaling Policy
# ------------------------------------------------------------------------------


class LoadBasedScalingPolicy(ScalingPolicy):
    """
    Scaling policy based on resource load metrics.

    This policy triggers scaling actions based on CPU, memory, and async pool
    utilization thresholds.
    """

    def __init__(
        self,
        cpu_high_threshold: float = 80.0,
        cpu_low_threshold: float = 20.0,
        memory_high_threshold: float = 85.0,
        memory_low_threshold: float = 30.0,
        async task_pool_high_threshold: float = 90.0,
        async task_pool_low_threshold: float = 40.0,
        min_scale_interval_seconds: float = 60.0,
    ):
        """
        Initialize load-based scaling policy.

        Args:
            cpu_high_threshold: CPU usage threshold for scaling up (%)
            cpu_low_threshold: CPU usage threshold for scaling down (%)
            memory_high_threshold: Memory usage threshold for scaling up (%)
            memory_low_threshold: Memory usage threshold for scaling down (%)
            async task_pool_high_threshold: Async pool usage threshold for scaling up (%)
            async task_pool_low_threshold: Async pool usage threshold for scaling down (%)
            min_scale_interval_seconds: Minimum time between scaling actions (seconds)
        """
        self.cpu_high_threshold = cpu_high_threshold
        self.cpu_low_threshold = cpu_low_threshold
        self.memory_high_threshold = memory_high_threshold
        self.memory_low_threshold = memory_low_threshold
        self.thread_pool_high_threshold = async task_pool_high_threshold
        self.thread_pool_low_threshold = async task_pool_low_threshold
        self.min_scale_interval_seconds = min_scale_interval_seconds

        # Track last scaling time for each resource type
        self.last_scaling_time: Dict[ResourceType, float] = {
            ResourceType.CPU: 0.0,
            ResourceType.MEMORY: 0.0,
            ResourceType.THREADS: 0.0,
            ResourceType.PROCESSES: 0.0,
        }

        # Scaling factors
        self.scale_up_factor = 1.5  # Scale up by 50%
        self.scale_down_factor = 0.7  # Scale down by 30%

        # Severity calculation functions
        self.severity_functions: Dict[ResourceType, Callable[[float, float], float]] = {
            ResourceType.CPU: self._calculate_cpu_severity,
            ResourceType.MEMORY: self._calculate_memory_severity,
            ResourceType.THREADS: self._calculate_thread_severity,
            ResourceType.PROCESSES: self._calculate_process_severity,
        }

    def evaluate_metrics(self, metrics: Dict[str, Any]) -> List[ScalingTrigger]:
        """
        Evaluate metrics to determine if scaling is needed.

        Args:
            metrics: Dictionary of metrics to evaluate

        Returns:
            List of scaling triggers
        """
        current_time = time.time()
        triggers: List[ScalingTrigger] = []

        # CPU Scaling
        if "cpu_usage" in metrics and ResourceType.CPU in self.last_scaling_time:
            cpu_usage = metrics["cpu_usage"]

            # Check if we've waited long enough since last scaling
            if (
                current_time - self.last_scaling_time[ResourceType.CPU]
                >= self.min_scale_interval_seconds
            ):
                if cpu_usage >= self.cpu_high_threshold:
                    # Need to scale up CPU
                    severity = self._calculate_cpu_severity(
                        cpu_usage, self.cpu_high_threshold
                    )
                    triggers.append(
                        ScalingTrigger(
                            resource_type=ResourceType.CPU,
                            direction=ScalingDirection.UP,
                            reason=f"CPU usage {cpu_usage:.1f}% above threshold {self.cpu_high_threshold:.1f}%",
                            severity=severity,
                            metric_value=cpu_usage,
                            threshold=self.cpu_high_threshold,
                        )
                    )
                elif cpu_usage <= self.cpu_low_threshold:
                    # Can scale down CPU
                    severity = self._calculate_cpu_severity(
                        self.cpu_low_threshold, cpu_usage
                    )
                    triggers.append(
                        ScalingTrigger(
                            resource_type=ResourceType.CPU,
                            direction=ScalingDirection.DOWN,
                            reason=f"CPU usage {cpu_usage:.1f}% below threshold {self.cpu_low_threshold:.1f}%",
                            severity=severity,
                            metric_value=cpu_usage,
                            threshold=self.cpu_low_threshold,
                        )
                    )

        # Memory Scaling
        if "memory_usage" in metrics and ResourceType.MEMORY in self.last_scaling_time:
            memory_usage = metrics["memory_usage"]

            # Check if we've waited long enough since last scaling
            if (
                current_time - self.last_scaling_time[ResourceType.MEMORY]
                >= self.min_scale_interval_seconds
            ):
                if memory_usage >= self.memory_high_threshold:
                    # Need to scale up Memory
                    severity = self._calculate_memory_severity(
                        memory_usage, self.memory_high_threshold
                    )
                    triggers.append(
                        ScalingTrigger(
                            resource_type=ResourceType.MEMORY,
                            direction=ScalingDirection.UP,
                            reason=f"Memory usage {memory_usage:.1f}% above threshold {self.memory_high_threshold:.1f}%",
                            severity=severity,
                            metric_value=memory_usage,
                            threshold=self.memory_high_threshold,
                        )
                    )
                elif memory_usage <= self.memory_low_threshold:
                    # Can scale down Memory
                    severity = self._calculate_memory_severity(
                        self.memory_low_threshold, memory_usage
                    )
                    triggers.append(
                        ScalingTrigger(
                            resource_type=ResourceType.MEMORY,
                            direction=ScalingDirection.DOWN,
                            reason=f"Memory usage {memory_usage:.1f}% below threshold {self.memory_low_threshold:.1f}%",
                            severity=severity,
                            metric_value=memory_usage,
                            threshold=self.memory_low_threshold,
                        )
                    )

        # Async task Pool Scaling
        if (
            "thread_pool_usage" in metrics
            and ResourceType.THREADS in self.last_scaling_time
        ):
            async task_pool_usage = metrics["thread_pool_usage"]

            # Check if we've waited long enough since last scaling
            if (
                current_time - self.last_scaling_time[ResourceType.THREADS]
                >= self.min_scale_interval_seconds
            ):
                if async task_pool_usage >= self.thread_pool_high_threshold:
                    # Need to scale up Async task Pool
                    severity = self._calculate_thread_severity(
                        async task_pool_usage, self.thread_pool_high_threshold
                    )
                    triggers.append(
                        ScalingTrigger(
                            resource_type=ResourceType.THREADS,
                            direction=ScalingDirection.UP,
                            reason=f"Async pool usage {thread_pool_usage:.1f}% above threshold {self.thread_pool_high_threshold:.1f}%",
                            severity=severity,
                            metric_value=thread_pool_usage,
                            threshold=self.thread_pool_high_threshold,
                        )
                    )
                elif async task_pool_usage <= self.thread_pool_low_threshold:
                    # Can scale down Async task Pool
                    severity = self._calculate_thread_severity(
                        self.thread_pool_low_threshold, async task_pool_usage
                    )
                    triggers.append(
                        ScalingTrigger(
                            resource_type=ResourceType.THREADS,
                            direction=ScalingDirection.DOWN,
                            reason=f"Async pool usage {thread_pool_usage:.1f}% below threshold {self.thread_pool_low_threshold:.1f}%",
                            severity=severity,
                            metric_value=thread_pool_usage,
                            threshold=self.thread_pool_low_threshold,
                        )
                    )

        return triggers

    def create_scaling_action(self, trigger: ScalingTrigger) -> ScalingAction:
        """
        Create a scaling action based on a trigger.

        Args:
            trigger: Scaling trigger

        Returns:
            Scaling action to take
        """
        # Update last scaling time
        self.last_scaling_time[trigger.resource_type] = time.time()

        # Determine scaling magnitude based on resource type and direction
        magnitude = (
            self.scale_up_factor
            if trigger.direction == ScalingDirection.UP
            else self.scale_down_factor
        )

        # Create action with resource-specific constraints
        action = ScalingAction(
            resource_type=trigger.resource_type,
            direction=trigger.direction,
            magnitude=magnitude,
            reason=trigger.reason,
            is_percentage=True,  # Use percentage scaling
        )

        # Add resource-specific constraints
        if trigger.resource_type == ResourceType.CPU:
            # For CPU, ensure we don't go below 1 core or above available cores
            import multiprocessing

            max_cores = multiprocessing.cpu_count()
            action.max_value = max_cores
            action.min_value = 1
        elif trigger.resource_type == ResourceType.MEMORY:
            # For memory, scaling usually handled by the runtime
            pass
        elif trigger.resource_type == ResourceType.THREADS:
            # For async tasks, ensure reasonable limits based on CPU cores
            import multiprocessing

            cpu_count = multiprocessing.cpu_count()
            action.min_value = cpu_count  # At least one async task per core
            action.max_value = cpu_count * 4  # Max 4 async tasks per core
        elif trigger.resource_type == ResourceType.PROCESSES:
            # For processes, limit to CPU cores
            import multiprocessing

            cpu_count = multiprocessing.cpu_count()
            action.min_value = 1
            action.max_value = cpu_count

        return action

    def _calculate_cpu_severity(self, current: float, threshold: float) -> float:
        """
        Calculate severity for CPU scaling.

        Args:
            current: Current value
            threshold: Threshold value

        Returns:
            Severity score (0.0-1.0)
        """
        # Difference from threshold as percentage of range
        if current > threshold:
            # Scaling up
            return min(1.0, (current - threshold) / (100.0 - threshold))
        else:
            # Scaling down
            return min(1.0, (threshold - current) / threshold)

    def _calculate_memory_severity(self, current: float, threshold: float) -> float:
        """
        Calculate severity for memory scaling.

        Args:
            current: Current value
            threshold: Threshold value

        Returns:
            Severity score (0.0-1.0)
        """
        # Memory scaling is more critical at high usage
        if current > threshold:
            # Scaling up - more urgent as we approach 100%
            return min(1.0, ((current - threshold) / (100.0 - threshold)) ** 0.5)
        else:
            # Scaling down - less urgent
            return min(1.0, ((threshold - current) / threshold) ** 2)

    def _calculate_thread_severity(self, current: float, threshold: float) -> float:
        """
        Calculate severity for async task scaling.

        Args:
            current: Current value
            threshold: Threshold value

        Returns:
            Severity score (0.0-1.0)
        """
        # Async task scaling severity is linear
        if current > threshold:
            return min(1.0, (current - threshold) / (100.0 - threshold))
        else:
            return min(1.0, (threshold - current) / threshold)

    def _calculate_process_severity(self, current: float, threshold: float) -> float:
        """
        Calculate severity for process scaling.

        Args:
            current: Current value
            threshold: Threshold value

        Returns:
            Severity score (0.0-1.0)
        """
        # Process scaling severity is moderate
        if current > threshold:
            return min(1.0, (current - threshold) / (100.0 - threshold) * 0.8)
        else:
            return min(1.0, (threshold - current) / threshold * 0.7)


# ------------------------------------------------------------------------------
# Adaptive Scaler
# ------------------------------------------------------------------------------


class AdaptiveScaler:
    """
    Main component for adaptive resource scaling.

    This class monitors system metrics and dynamically adjusts resource allocations
    based on workload demands and system load.
    """

    def __init__(self, policy: Optional[ScalingPolicy] = None):
        """
        Initialize adaptive scaler.

        Args:
            policy: Scaling policy to use, or None for default
        """
        self.logger = logging.getLogger(
            "person_suit.resource_optimization.compute.adaptive"
        )
        self._lock = asyncio.Lock()

        # Use provided policy or create default
        self.policy = policy or LoadBasedScalingPolicy()

        # Resource allocator
        self.allocator = get_compute_allocator()

        # Resource tracking
        self._resources: Dict[str, Dict[str, Any]] = {
            "cores": {},
            "threads": {},
            "processes": {},
        }

        # Metrics
        self._metrics = {
            "scaling_actions": Counter(
                name="resource_optimization.scaling.actions.count",
                description="Number of scaling actions performed",
            ),
            "scaling_success": Counter(
                name="resource_optimization.scaling.success.count",
                description="Number of successful scaling actions",
            ),
            "scaling_failures": Counter(
                name="resource_optimization.scaling.failures.count",
                description="Number of failed scaling actions",
            ),
            "scaling_time": Timer(
                name="resource_optimization.scaling.execution_time",
                description="Time to execute scaling actions",
            ),
            "current_cores": Gauge(
                name="resource_optimization.current.cores",
                description="Current number of cores allocated",
            ),
            "current_threads": Gauge(
                name="resource_optimization.current.threads",
                description="Current number of async tasks allocated",
            ),
            "current_processes": Gauge(
                name="resource_optimization.current.processes",
                description="Current number of processes allocated",
            ),
        }

        # Register metrics
        metrics_registry = get_metrics_registry()
        for metric in self._metrics.values():
            metrics_registry.register_metric(metric)

        # Initialize scaling components
        self._initialize()

    def _initialize(self) -> None:
        """Initialize scaler components."""
        # Initialize metrics
        self._metrics["current_cores"].set(0)
        self._metrics["current_threads"].set(0)
        self._metrics["current_processes"].set(0)

    def evaluate_system(self) -> List[ScalingTrigger]:
        """
        Evaluate the system to determine if scaling is needed.

        Returns:
            List of scaling triggers
        """
        metrics = self._collect_metrics()
        return self.policy.evaluate_metrics(metrics)

    def _collect_metrics(self) -> Dict[str, float]:
        """
        Collect metrics for scaling decisions.

        Returns:
            Dictionary of metrics
        """
        metrics = {}

        # Collect CPU and memory metrics from system collector
        if self._system_collector:
            try:
                # Update metrics
                self._system_collector.collect_metrics()

                # Get CPU and memory usage
                metrics["cpu_usage"] = self._system_collector.metrics[
                    "cpu_usage"
                ].get_value()
                metrics["memory_usage"] = self._system_collector.metrics[
                    "memory_usage"
                ].get_value()

                # Get process-specific metrics if available
                if "process_cpu" in self._system_collector.metrics:
                    metrics["process_cpu"] = self._system_collector.metrics[
                        "process_cpu"
                    ].get_value()

                if "process_memory" in self._system_collector.metrics:
                    metrics["process_memory"] = self._system_collector.metrics[
                        "process_memory"
                    ].get_value()
            except Exception as e:
                self.logger.error(f"Error collecting system metrics: {e}")
        else:
            # Fallback to psutil if system collector not available
            import psutil

            metrics["cpu_usage"] = psutil.cpu_percent()
            metrics["memory_usage"] = psutil.virtual_memory().percent

        # Add async pool metrics if available
        try:
            # TODO: Fix thread pool collector import - needs architectural review
            # The get_thread_pool_collector was imported from message_based_imports which no longer exists
            # Need to determine correct thread pool collector location
            # from person_suit.core.infrastructure.message_based_imports import (
            #     get_thread_pool_collector,
            # )
            # 
            # task_pool_collector = get_thread_pool_collector()
            # active_threads = task_pool_collector.metrics["active_threads"].get_value()
            # queue_size = task_pool_collector.metrics["queue_size"].get_value()
            
            # For now, set default values to avoid errors
            active_threads = 0
            queue_size = 0

            # Calculate async pool usage as ratio of active to total async tasks
            total_threads = active_threads + queue_size
            if total_threads > 0:
                metrics["thread_pool_usage"] = (active_threads / total_threads) * 100.0
            else:
                metrics["thread_pool_usage"] = 0.0
        except Exception as e:
            self.logger.debug(f"Async pool metrics not available: {e}")

        # Add current allocation metrics
        metrics["current_cores"] = self._metrics["current_cores"].get_value()
        metrics["current_threads"] = self._metrics["current_threads"].get_value()
        metrics["current_processes"] = self._metrics["current_processes"].get_value()

        return metrics

    def apply_scaling_action(self, action: ScalingAction) -> ScalingResult:
        """
        Apply a scaling action.

        Args:
            action: Scaling action to apply

        Returns:
            Result of the scaling action
        """
        start_time = time.time()
        self._metrics["scaling_actions"].increment()

        try:
            # Determine how to scale based on resource type
            if action.resource_type == ResourceType.CPU:
                result = self._scale_cpu_cores(action)
            elif action.resource_type == ResourceType.MEMORY:
                result = self._scale_memory(action)
            elif action.resource_type == ResourceType.THREADS:
                result = self._scale_threads(action)
            elif action.resource_type == ResourceType.PROCESSES:
                result = self._scale_processes(action)
            else:
                result = ScalingResult(
                    action=action,
                    success=False,
                    message=f"Unknown resource type: {action.resource_type}",
                )

            # Record result
            if result.success:
                self._metrics["scaling_success"].increment()
            else:
                self._metrics["scaling_failures"].increment()

            # Record execution time
            execution_time = time.time() - start_time
            self._metrics["scaling_time"].record(execution_time * 1000)  # Convert to ms
            result.execution_time = execution_time

            # Log the result
            if result.success:
                self.logger.info(
                    f"Successfully applied scaling action: {result.message}"
                )
            else:
                self.logger.warning(f"Failed to apply scaling action: {result.message}")

            return result
        except Exception as e:
            # Handle any errors
            self._metrics["scaling_failures"].increment()
            execution_time = time.time() - start_time

            error_message = f"Error applying scaling action: {str(e)}"
            self.logger.error(error_message, exc_info=True)

            return ScalingResult(
                action=action,
                success=False,
                message=error_message,
                execution_time=execution_time,
            )

    def _scale_cpu_cores(self, action: ScalingAction) -> ScalingResult:
        """
        Scale CPU core allocation.

        Args:
            action: Scaling action

        Returns:
            Result of the scaling action
        """
        # Get current CPU allocation
        current_cores = int(self._metrics["current_cores"].get_value())

        # Calculate new core count
        if action.is_percentage:
            # Scale by percentage
            new_cores = int(current_cores * action.magnitude)
        else:
            # Absolute scaling
            if action.direction == ScalingDirection.UP:
                new_cores = current_cores + int(action.magnitude)
            else:
                new_cores = current_cores - int(action.magnitude)

        # Apply constraints
        if action.max_value is not None:
            new_cores = min(new_cores, action.max_value)

        if action.min_value is not None:
            new_cores = max(new_cores, action.min_value)

        # Ensure at least 1 core
        new_cores = max(1, new_cores)

        # Only scale if there's a change
        if new_cores == current_cores:
            return ScalingResult(
                action=action,
                success=True,
                message=f"No change needed in core count: {current_cores}",
            )

        # Allocate cores
        try:
            # If scaling down, we'll release some allocations
            if new_cores < current_cores:
                # Get allocations to release
                system_info = self.allocator.get_system_info()
                system_info["allocations"]["cores"]

                # For now, we'll just release the most recent allocation
                # A more sophisticated approach would consider which allocations to release

                # Since we don't have direct access to the allocations, we'll just
                # track the new core count and let the allocator handle it

                # Update metrics
                self._metrics["current_cores"].set(new_cores)

                return ScalingResult(
                    action=action,
                    success=True,
                    message=f"Scaled down cores from {current_cores} to {new_cores}",
                )
            else:
                # Scaling up
                cores_to_add = new_cores - current_cores
                new_allocation = self.allocator.allocate_cores(cores_to_add)

                # Update metrics
                self._metrics["current_cores"].set(new_cores)

                return ScalingResult(
                    action=action,
                    success=True,
                    message=f"Scaled up cores from {current_cores} to {new_cores}",
                    new_allocation=new_allocation,
                )
        except Exception as e:
            return ScalingResult(
                action=action, success=False, message=f"Failed to scale cores: {str(e)}"
            )

    def _scale_memory(self, action: ScalingAction) -> ScalingResult:
        """
        Scale memory allocation.

        Note: Memory scaling is often handled by the runtime, so this may have limited effect

        Args:
            action: Scaling action

        Returns:
            Result of the scaling action
        """
        # Memory scaling is mostly advisory in Python
        # We can adjust some runtime parameters but direct control is limited
        import gc

        # Force garbage collection
        gc.collect()

        # Return success
        return ScalingResult(
            action=action,
            success=True,
            message="Applied memory optimization (garbage collection)",
        )

    def _scale_threads(self, action: ScalingAction) -> ScalingResult:
        """
        Scale async task allocation.

        Args:
            action: Scaling action

        Returns:
            Result of the scaling action
        """
        # Get current async task allocation
        current_threads = int(self._metrics["current_threads"].get_value())

        # Calculate new async task count
        if action.is_percentage:
            # Scale by percentage
            new_threads = int(current_threads * action.magnitude)
        else:
            # Absolute scaling
            if action.direction == ScalingDirection.UP:
                new_threads = current_threads + int(action.magnitude)
            else:
                new_threads = current_threads - int(action.magnitude)

        # Apply constraints
        if action.max_value is not None:
            new_threads = min(new_threads, action.max_value)

        if action.min_value is not None:
            new_threads = max(new_threads, action.min_value)

        # Ensure at least 1 async task
        new_threads = max(1, new_threads)

        # Only scale if there's a change
        if new_threads == current_threads:
            return ScalingResult(
                action=action,
                success=True,
                message=f"No change needed in async task count: {current_threads}",
            )

        # Allocate async tasks
        try:
            # For async task allocation, we need to determine how to scale the specific async pool
            # This would typically involve calling into the async pool manager

            # For now, we'll just track the new async task count
            # In a real implementation, this would resize the async pool

            # Update metrics
            self._metrics["current_threads"].set(new_threads)

            return ScalingResult(
                action=action,
                success=True,
                message=f"Scaled async pool from {current_threads} to {new_threads}",
            )
        except Exception as e:
            return ScalingResult(
                action=action,
                success=False,
                message=f"Failed to scale async tasks: {str(e)}",
            )

    def _scale_processes(self, action: ScalingAction) -> ScalingResult:
        """
        Scale process allocation.

        Args:
            action: Scaling action

        Returns:
            Result of the scaling action
        """
        # Get current process allocation
        current_processes = int(self._metrics["current_processes"].get_value())

        # Calculate new process count
        if action.is_percentage:
            # Scale by percentage
            new_processes = int(current_processes * action.magnitude)
        else:
            # Absolute scaling
            if action.direction == ScalingDirection.UP:
                new_processes = current_processes + int(action.magnitude)
            else:
                new_processes = current_processes - int(action.magnitude)

        # Apply constraints
        if action.max_value is not None:
            new_processes = min(new_processes, action.max_value)

        if action.min_value is not None:
            new_processes = max(new_processes, action.min_value)

        # Ensure at least 1 process
        new_processes = max(1, new_processes)

        # Only scale if there's a change
        if new_processes == current_processes:
            return ScalingResult(
                action=action,
                success=True,
                message=f"No change needed in process count: {current_processes}",
            )

        # Allocate processes
        try:
            if new_processes < current_processes:
                # Scaling down processes typically involves signaling them to shut down
                # This would be very application-specific

                # Update metrics
                self._metrics["current_processes"].set(new_processes)

                return ScalingResult(
                    action=action,
                    success=True,
                    message=f"Scaled down processes from {current_processes} to {new_processes} (advisory)",
                )
            else:
                # Scaling up processes
                new_processes - current_processes

                # This would typically launch new processes, but we'll just track the count
                # In a real implementation, this would create new processes

                # Update metrics
                self._metrics["current_processes"].set(new_processes)

                return ScalingResult(
                    action=action,
                    success=True,
                    message=f"Scaled up processes from {current_processes} to {new_processes} (advisory)",
                )
        except Exception as e:
            return ScalingResult(
                action=action,
                success=False,
                message=f"Failed to scale processes: {str(e)}",
            )

    def run_evaluation_cycle(self) -> List[ScalingResult]:
        """
        Run a complete evaluation and scaling cycle.

        Returns:
            List of scaling results
        """
        # Evaluate system
        triggers = self.evaluate_system()

        # Apply scaling actions for actionable triggers
        results = []
        for trigger in triggers:
            if trigger.is_actionable:
                action = self.policy.create_scaling_action(trigger)
                result = self.apply_scaling_action(action)
                results.append(result)

        return results

    def get_health_status(self) -> HealthStatus:
        """
        Get health status of the adaptive scaler.

        Returns:
            Health status
        """
        # Check if we have monitoring integration
        if self._system_collector is None:
            return HealthStatus.DEGRADED

        # Check recent scaling actions for failures
        success_count = self._metrics["scaling_success"].get_value()
        failure_count = self._metrics["scaling_failures"].get_value()
        total_count = success_count + failure_count

        if total_count == 0:
            # No scaling actions yet
            status = HealthStatus.HEALTHY
            message = "No scaling actions performed yet"
        elif failure_count > 0:
            # Some failures
            failure_rate = failure_count / total_count
            if failure_rate > 0.5:
                status = HealthStatus.UNHEALTHY
                message = f"High scaling failure rate: {failure_rate:.1%}"
            else:
                status = HealthStatus.DEGRADED
                message = f"Some scaling failures: {failure_count} out of {total_count}"
        else:
            # All successes
            status = HealthStatus.HEALTHY
            message = f"All scaling actions successful: {success_count}"

        # Get current resource allocation
        current_cores = self._metrics["current_cores"].get_value()
        current_threads = self._metrics["current_threads"].get_value()
        current_processes = self._metrics["current_processes"].get_value()

        return HealthStatus(
            status=status,
            message=message,
            details={
                "monitoring_available": True,
                "scaling_actions": {
                    "total": total_count,
                    "success": success_count,
                    "failures": failure_count,
                },
                "current_allocation": {
                    "cores": current_cores,
                    "threads": current_threads,
                    "processes": current_processes,
                },
            },
        )


# ------------------------------------------------------------------------------
# Module-level functions
# ------------------------------------------------------------------------------

# Singleton instance
_adaptive_scaler = None


def get_adaptive_scaler() -> AdaptiveScaler:
    """
    Get the singleton adaptive scaler.

    Returns:
        AdaptiveScaler instance
    """
    global _adaptive_scaler
    if _adaptive_scaler is None:
        _adaptive_scaler = AdaptiveScaler()
    return _adaptive_scaler
