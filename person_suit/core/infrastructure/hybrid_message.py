"""
hybrid_message.py - Hybrid Message Infrastructure for Person Suit
================================================================

Implements the hybrid message bus architecture combining:
- Channel-based routing for fast message delivery
- ACF metadata for adaptive behavior
- Zero imports between components
- Wave-particle duality built into every message

This is the core of the true message-based, choreographed architecture.

Related Files:
- message_based_core.py: Core message bus
- channel_registry.py: Channel definitions
- docs/architecture/hybrid_message_bus_implementation.md: Full plan

Dependencies:
- Standard library only (zero internal imports!)
"""

import base64
import hashlib
import json
import logging
import time
import uuid
from dataclasses import dataclass
from dataclasses import field
from enum import Enum
from enum import auto
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional
from typing import Union

from person_suit.core.constants.fixed_point_scale import PRIO_CRITICAL
from person_suit.core.constants.fixed_point_scale import PRIO_HIGH
from person_suit.core.constants.fixed_point_scale import PRIO_LOW
from person_suit.core.constants.fixed_point_scale import PRIO_NORMAL
from person_suit.core.constants.fixed_point_scale import SCALE

# Dual representation models are now imported lazily to avoid circular dependencies
# and to handle optional installations gracefully.

logger = logging.getLogger(__name__)

# ============================================================================
# Message Priority System
# ============================================================================


class Priority:
    """Priority handling with integer bucket optimization."""

    # Class constants for direct use.
    CRITICAL = PRIO_CRITICAL
    HIGH = PRIO_HIGH
    NORMAL = PRIO_NORMAL
    LOW = PRIO_LOW
    DEFERRED = int(0.05 * SCALE)

    @staticmethod
    def validate(priority: int) -> int:
        """Ensure priority is a valid integer bucket."""
        if not isinstance(priority, int):
            raise TypeError(f"Priority must be an integer, not {type(priority).__name__}")
        return max(0, min(SCALE, priority))


# ============================================================================
# Channel Type Definitions
# ============================================================================


class ChannelType(Enum):
    """Predefined channel types for type safety and fast routing."""

    # Persona Core Channels
    PC_MEMORY = "pc.memory"
    PC_MEMORY_ENCODE = "pc.memory.encode"
    PC_MEMORY_RETRIEVE = "pc.memory.retrieve"
    PC_MEMORY_SEARCH = "pc.memory.search"

    PC_EMOTION = "pc.emotion"
    PC_EMOTION_ANALYZE = "pc.emotion.analyze"
    PC_EMOTION_PROCESS = "pc.emotion.process"
    PC_EMOTION_REGULATE = "pc.emotion.regulate"

    PC_FOLDED_MIND = "pc.folded_mind"
    PC_FOLDED_MIND_CAM = "pc.folded_mind.cam"
    PC_FOLDED_MIND_CAM_ANALYZE = "pc.folded_mind.cam.analyze"
    PC_FOLDED_MIND_CAM_REASON = "pc.folded_mind.cam.reason"
    PC_FOLDED_MIND_SEM = "pc.folded_mind.sem"
    PC_FOLDED_MIND_SEM_INTUIT = "pc.folded_mind.sem.intuit"
    PC_FOLDED_MIND_SEM_METAPHOR = "pc.folded_mind.sem.metaphor"
    PC_FOLDED_MIND_SOMA = "pc.folded_mind.soma"
    PC_FOLDED_MIND_SOMA_SENSE = "pc.folded_mind.soma.sense"
    PC_FOLDED_MIND_SOMA_EMBODY = "pc.folded_mind.soma.embody"
    PC_FOLDED_MIND_INTEGRATE = "pc.folded_mind.integrate"
    PC_FOLDED_MIND_ARBITRATE = "pc.folded_mind.arbitrate"

    PC_COGNITION = "pc.cognition"
    PC_COGNITION_REASON = "pc.cognition.reason"
    PC_COGNITION_DECIDE = "pc.cognition.decide"

    # Analyst Channels
    AN_PATTERN = "an.pattern"
    AN_PATTERN_DETECT = "an.pattern.detect"
    AN_PATTERN_LINGUISTIC = "an.pattern.linguistic"
    AN_PATTERN_EMOTIONAL = "an.pattern.emotional"

    AN_ENTITY = "an.entity"
    AN_ENTITY_TRACK = "an.entity.track"
    AN_ENTITY_RECOGNIZE = "an.entity.recognize"

    AN_CONTEXT = "an.context"
    AN_CONTEXT_EXTRACT = "an.context.extract"
    AN_CONTEXT_UPDATE = "an.context.update"

    AN_TOKEN = "an.token"
    AN_TOKEN_ANALYZE = "an.token.analyze"

    # Predictor Channels
    PR_MODEL = "pr.model"
    PR_MODEL_INFER = "pr.model.infer"
    PR_MODEL_TRAIN = "pr.model.train"
    PR_MODEL_PREDICT = "pr.model.predict"

    PR_HYPOTHESIS = "pr.hypothesis"
    PR_HYPOTHESIS_GENERATE = "pr.hypothesis.generate"
    PR_HYPOTHESIS_TEST = "pr.hypothesis.test"

    PR_TREND = "pr.trend"
    PR_TREND_ANALYZE = "pr.trend.analyze"

    # System Channels
    SYS_HEALTH = "sys.health"
    SYS_HEALTH_CHECK = "sys.health.check"
    SYS_HEALTH_REPORT = "sys.health.report"

    SYS_COORDINATE = "sys.coordinate"
    SYS_COORDINATE_REQUEST = "sys.coordinate.request"
    SYS_COORDINATE_SYNC = "sys.coordinate.sync"

    SYS_ACF = "sys.acf"
    SYS_ACF_ADJUST = "sys.acf.adjust"
    SYS_ACF_REPORT = "sys.acf.report"

    SYS_MONITOR = "sys.monitor"
    SYS_MONITOR_METRICS = "sys.monitor.metrics"
    SYS_MONITOR_ALERT = "sys.monitor.alert"

    # Resource Management Channels (NEW)
    SYS_RESOURCE_CAPACITY_UPDATE = "sys.resource.capacity.update"
    SYS_RESOURCE_REQUEST = "sys.resource.request"
    SYS_RESOURCE_RESPONSE = (
        "sys.resource.response"  # Generic response channel, specific replies use reply_to
    )
    SYS_RESOURCE_TELEMETRY = "sys.resource.telemetry"

    # Special Channels
    BROADCAST = "broadcast"  # Send to all subscribers
    ERROR = "error"  # Error messages
    DEAD_LETTER = "dead_letter"  # Undeliverable messages


# ============================================================================
# ACF Metadata
# ============================================================================


@dataclass
class ACFMetadata:
    """Adaptive Computational Fidelity metadata for message processing."""

    fidelity: int = SCALE  # Current fidelity bucket
    min_fidelity: int = int(0.3 * SCALE)  # Minimum acceptable fidelity
    max_fidelity: int = SCALE  # Maximum allowed fidelity

    # Resource hints
    cpu_intensive: bool = False
    memory_intensive: bool = False
    io_intensive: bool = False
    gpu_capable: bool = False

    # Adaptation strategy
    can_degrade: bool = True  # Can reduce fidelity under load?
    prefers_cache: bool = False  # Prefer cached results?
    timeout_flexible: bool = True  # Can adjust timeout?
    parallelizable: bool = True  # Can process in parallel?

    # Quality requirements
    accuracy_required: float = 0.8  # Minimum accuracy needed
    completeness_required: float = 0.9  # How complete must results be?

    def adjust_for_load(self, system_load: float) -> None:
        """Adjust fidelity based on system load."""
        # Convert system_load to integer scale for efficient comparison
        system_load_int = int(system_load * SCALE) if system_load <= 1.0 else SCALE

        # Define thresholds in integer scale
        HIGH_LOAD_THRESHOLD = int(0.8 * SCALE)  # 800_000
        MEDIUM_LOAD_THRESHOLD = int(0.6 * SCALE)  # 600_000
        LOW_LOAD_THRESHOLD = int(0.3 * SCALE)  # 300_000

        if system_load_int > HIGH_LOAD_THRESHOLD:
            # High load – heavy degradation (×0.7)
            self.fidelity = max(self.min_fidelity, (self.fidelity * 7) // 10)
        elif system_load_int > MEDIUM_LOAD_THRESHOLD:
            # Medium load – mild degradation (×0.85)
            self.fidelity = max(self.min_fidelity, (self.fidelity * 85) // 100)
        elif system_load_int < LOW_LOAD_THRESHOLD:
            # Low load – opportunity to boost (×1.15)
            self.fidelity = min(self.max_fidelity, (self.fidelity * 115) // 100)

    def should_use_cache(self, cache_age_seconds: float) -> bool:
        """Determine if cached results are acceptable."""
        if not self.prefers_cache:
            return False

        # More lenient with cache at lower fidelity (using integer arithmetic)
        # Formula: 300 * (2 * SCALE - fidelity) / SCALE = 300-600 seconds
        max_cache_age = 300 * (2 * SCALE - self.fidelity) // SCALE
        return cache_age_seconds < max_cache_age

    def get_timeout_ms(self, base_timeout_ms: float) -> float:
        """Calculate adjusted timeout based on fidelity."""
        if not self.timeout_flexible:
            return base_timeout_ms

        # Lower fidelity = shorter timeout (using integer arithmetic)
        return base_timeout_ms * self.fidelity // SCALE


# ============================================================================
# Routing Hints
# ============================================================================


@dataclass
class RoutingHints:
    """Hints for intelligent message routing."""

    load_sensitive: bool = True  # Should consider system load?
    alternative_channels: List[str] = field(default_factory=list)  # Fallback channels
    preferred_handler: Optional[str] = None  # Specific handler preference
    affinity_key: Optional[str] = None  # For sticky routing
    broadcast: bool = False  # Send to all handlers?
    priority_boost: float = 0.0  # Priority adjustment (-1.0 to 1.0)

    # Routing strategies
    strategy: str = "fastest"  # fastest | roundrobin | leastloaded | sticky
    max_hops: int = 3  # Maximum routing hops

    def add_alternative(self, channel: str) -> None:
        """Add an alternative channel for fallback routing."""
        if channel not in self.alternative_channels:
            self.alternative_channels.append(channel)

    def should_retry_on_failure(self) -> bool:
        """Determine if message should be retried on failure."""
        return len(self.alternative_channels) > 0 or self.load_sensitive


# ============================================================================
# Execution Constraints
# ============================================================================


@dataclass
class ExecutionConstraints:
    """Constraints on how a message should be executed."""

    max_latency_ms: Optional[float] = None  # Maximum acceptable latency
    requires_ordering: bool = False  # Must maintain order?
    idempotent: bool = True  # Safe to retry?
    ttl_seconds: float = 300  # Time to live
    max_retries: int = 3  # Maximum retry attempts
    retry_strategy: str = "exponential"  # exponential | linear | fixed

    # Execution requirements
    requires_ack: bool = True  # Need acknowledgment?
    fire_and_forget: bool = False  # Don't wait for response?
    exactly_once: bool = False  # Guarantee exactly once delivery?

    # Resource limits
    max_memory_mb: Optional[float] = None  # Memory limit
    max_cpu_seconds: Optional[float] = None  # CPU time limit

    def is_expired(self) -> bool:
        """Check if message has expired based on TTL."""
        # Note: Would need message creation time to implement properly
        return False

    def get_retry_delay_ms(self, attempt: int) -> float:
        """Calculate retry delay based on strategy."""
        if self.retry_strategy == "exponential":
            return min(1000 * (2**attempt), 30000)  # Cap at 30s
        elif self.retry_strategy == "linear":
            return min(1000 * attempt, 10000)  # Cap at 10s
        else:  # fixed
            return 1000  # 1 second


# =========================================================================
# Message Type Enum (moved up so HybridMessage can reference it)
# =========================================================================


class MessageType(Enum):
    """Defines the classification of a HybridMessage."""

    EVENT = auto()
    COMMAND = auto()
    EFFECT = auto()
    REQUEST = auto()
    RESPONSE = auto()
    SYSTEM = auto()
    USER_INPUT = auto()
    INTERNAL_STATE = auto()


# =========================================================================
# Hybrid Message
# =========================================================================


@dataclass
class HybridMessage:
    """
    The core message type combining channels, ACF, and CAW principles.
    Refactored for simplicity and architectural purity.
    """

    # Identity
    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: float = field(default_factory=time.time)

    # Channel-based routing and classification
    # Explicit message type field required by EffectInterpreter and other
    # subsystems.  Defaults to EVENT for backward compatibility.
    message_type: MessageType = MessageType.EVENT  # Defaults to EVENT; caller may override

    # Channel-based routing
    channel: str = ""
    source_channel: Optional[str] = None

    # Content
    payload: Dict[str, Any] = field(default_factory=dict)

    # Priority is a simple, required integer bucket. No more floats or context derivation.
    priority: int = PRIO_NORMAL

    # Metadata for adaptation
    acf_metadata: ACFMetadata = field(default_factory=ACFMetadata)
    routing_hints: RoutingHints = field(default_factory=RoutingHints)
    execution_constraints: ExecutionConstraints = field(default_factory=ExecutionConstraints)

    # CAW properties
    wave_particle_ratio: float = 0.5

    # Context propagation
    context: Dict[str, Any] = field(default_factory=dict)
    correlation_id: Optional[str] = None
    causality_chain: List[str] = field(default_factory=list)

    # Other metadata
    metadata: Dict[str, Any] = field(default_factory=dict)

    # Response handling
    reply_channel: Optional[str] = None
    response_expected: bool = True

    # Capability token – during migration both string and Biscuit bytes are accepted.
    capability_token: Optional[Union[str, bytes]] = None

    # Dual representation
    wave_state: Optional[Any] = None
    particle_state: Optional[Any] = None

    # Provenance
    provenance_hash: Optional[str] = None

    # ------------------------------------------------------------------
    # WaveTrace Tracing (NEW)
    # ------------------------------------------------------------------
    # Trace identifiers follow W3C Trace Context specification semantics
    trace_id: str = field(default_factory=lambda: str(uuid.uuid4()))  # 16-byte UUID v4 hex
    span_id: str = field(default_factory=lambda: uuid.uuid4().hex[:16])  # 8-byte hex identifier
    parent_span_id: Optional[str] = None  # Span that spawned this message (if any)
    trace_flags: int = 0  # Bit-mask per W3C spec (e.g., sampled = 0x01)
    trace_state: Dict[str, str] = field(default_factory=dict)  # Vendor-specific tracing info

    # CAW computational branch – records whether message was processed via
    # wave or particle pathway.  Populated by the interpreter or actor that
    # makes the branch decision.  Possible values: "WAVE", "PARTICLE".
    caw_branch_taken: Optional[str] = None

    def __post_init__(self):
        """
        Validate and normalize the message after creation.
        Simplified to enforce architectural principles directly.
        """
        # 1. Enforce integer priority
        self.priority = Priority.validate(self.priority)

        # 2. Normalize channel from Enum to string if needed
        if isinstance(self.channel, Enum):
            self.channel = self.channel.value

        # 3. Compute provenance hash if not provided
        if self.provenance_hash is None:
            self._compute_provenance_hash()

        # 3b. Auto-decode capability token if it looks like base64
        if isinstance(self.capability_token, str):
            try:
                if len(self.capability_token) % 4 == 0:
                    decoded = base64.b64decode(self.capability_token)
                    # Simple heuristic: Biscuit tokens start with "BS" magic bytes in v2
                    if decoded[:1] in {b"B", b"\x00"}:
                        self.capability_token = decoded
            except Exception:
                pass

        # 4. Infer message_type from channel if not explicitly set
        # This is a safe way to handle the new `message_type` field without
        # breaking existing code that doesn't provide it.
        if not hasattr(self, "message_type") or getattr(self, "message_type", None) is None:
            if self.channel.startswith("command."):
                object.__setattr__(self, "message_type", MessageType.COMMAND)
            elif self.channel.startswith("effect."):
                object.__setattr__(self, "message_type", MessageType.EFFECT)
            elif self.channel.startswith("event."):
                object.__setattr__(self, "message_type", MessageType.EVENT)
            else:
                object.__setattr__(self, "message_type", MessageType.SYSTEM)

    def _compute_provenance_hash(self):
        try:
            stable_projection = json.dumps(
                {
                    "payload": self.payload,
                    "channel": self.channel,
                    "causality_chain": self.causality_chain,
                },
                default=str,
                sort_keys=True,
            ).encode()
            self.provenance_hash = hashlib.sha256(stable_projection).hexdigest()
        except Exception as exc:
            logger.debug("Could not compute provenance hash: %s", exc)

    @property
    def meta_system(self) -> str:
        """Extract meta-system from channel (pc, an, pr, sys)."""
        return self.channel.split(".")[0] if "." in self.channel else "unknown"

    @property
    def subsystem(self) -> str:
        """Extract subsystem from channel (e.g., 'memory' from 'pc.memory.encode')."""
        parts = self.channel.split(".")
        return parts[1] if len(parts) > 1 else ""

    @property
    def operation(self) -> str:
        """Extract operation from channel (e.g., 'encode' from 'pc.memory.encode')."""
        parts = self.channel.split(".")
        return parts[2] if len(parts) > 2 else ""

    def is_system_message(self) -> bool:
        """Check if this is a system-level message."""
        return self.meta_system == "sys"

    def is_broadcast(self) -> bool:
        """Check if this should be broadcast to all handlers."""
        return self.channel == "broadcast" or self.routing_hints.broadcast

    def is_error(self) -> bool:
        """Check if this is an error message."""
        return self.channel.startswith("error") or self.channel == "dead_letter"

    @property
    def requires_ack(self) -> bool:
        """Check if this message requires acknowledgment (delegates to execution_constraints)."""
        return self.execution_constraints.requires_ack

    def create_reply(
        self, payload: Dict[str, Any], channel: Optional[str] = None
    ) -> "HybridMessage":
        """Create a reply message maintaining context and correlation."""
        reply = HybridMessage(
            channel=channel or self.source_channel or "error",
            source_channel=self.channel,
            payload=payload,
            priority=self.priority,  # Replies inherit priority
            correlation_id=self.correlation_id or self.message_id,
            context=self.context.copy(),
            wave_particle_ratio=self.wave_particle_ratio,
            response_expected=False,
        )
        reply.causality_chain = self.causality_chain + [self.message_id]
        reply.acf_metadata.fidelity = self.acf_metadata.fidelity
        return reply

    def adjust_for_load(self, system_load: float) -> None:
        """Adjust message parameters based on system load."""
        self.acf_metadata.adjust_for_load(system_load)

        # Also adjust timeout if flexible
        if self.execution_constraints.max_latency_ms and self.acf_metadata.timeout_flexible:
            self.execution_constraints.max_latency_ms *= self.acf_metadata.fidelity / SCALE

    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary for serialization."""
        try:
            from person_suit.core.infrastructure.dual_wave.states import ParticleState
            from person_suit.core.infrastructure.dual_wave.states import WaveState
        except ImportError:
            WaveState, ParticleState = type(None), type(None)  # Fallback if not found

        return {
            "message_id": self.message_id,
            "timestamp": self.timestamp,
            "channel": self.channel,
            "source_channel": self.source_channel,
            "payload": self.payload,
            "priority": self.priority,
            "acf_metadata": {
                "fidelity": self.acf_metadata.fidelity,
                "min_fidelity": self.acf_metadata.min_fidelity,
                "can_degrade": self.acf_metadata.can_degrade,
                "cpu_intensive": self.acf_metadata.cpu_intensive,
                "memory_intensive": self.acf_metadata.memory_intensive,
            },
            "routing_hints": {
                "load_sensitive": self.routing_hints.load_sensitive,
                "alternative_channels": self.routing_hints.alternative_channels,
                "strategy": self.routing_hints.strategy,
            },
            "wave_particle_ratio": self.wave_particle_ratio,
            "context": self.context,
            "correlation_id": self.correlation_id,
            "causality_chain": self.causality_chain,
            "metadata": self.metadata,
            "wave_state": self.wave_state.__dict__
            if isinstance(self.wave_state, WaveState)
            else None,
            "particle_state": self.particle_state.__dict__
            if isinstance(self.particle_state, ParticleState)
            else None,
            "provenance_hash": self.provenance_hash,
            "capability_token": (
                base64.b64encode(self.capability_token).decode()
                if isinstance(self.capability_token, (bytes, bytearray))
                else self.capability_token
            ),
            # NEW – tracing context & CAW branch
            "trace_id": self.trace_id,
            "span_id": self.span_id,
            "parent_span_id": self.parent_span_id,
            "trace_flags": self.trace_flags,
            "trace_state": self.trace_state,
            "caw_branch_taken": self.caw_branch_taken,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "HybridMessage":
        """Create message from dictionary."""
        try:
            from person_suit.core.infrastructure.dual_wave.states import ParticleState
            from person_suit.core.infrastructure.dual_wave.states import WaveState
        except ImportError:
            WaveState, ParticleState = type(None), type(None)  # Fallback if not found

        msg = cls(
            message_id=data.get("message_id", str(uuid.uuid4())),
            timestamp=data.get("timestamp", time.time()),
            channel=data.get("channel", ""),
            source_channel=data.get("source_channel"),
            payload=data.get("payload", {}),
            priority=data.get("priority", PRIO_NORMAL),  # Default
            wave_particle_ratio=data.get("wave_particle_ratio", 0.5),
            context=data.get("context", {}),
            correlation_id=data.get("correlation_id"),
            causality_chain=data.get("causality_chain", []),
            metadata=data.get("metadata", {}),
        )

        # Restore ACF metadata
        if "acf_metadata" in data:
            acf = data["acf_metadata"]
            msg.acf_metadata.fidelity = acf.get("fidelity", SCALE)
            msg.acf_metadata.min_fidelity = acf.get("min_fidelity", int(0.3 * SCALE))
            msg.acf_metadata.can_degrade = acf.get("can_degrade", True)
            msg.acf_metadata.cpu_intensive = acf.get("cpu_intensive", False)
            msg.acf_metadata.memory_intensive = acf.get("memory_intensive", False)

        # Restore routing hints
        if "routing_hints" in data:
            hints = data["routing_hints"]
            msg.routing_hints.load_sensitive = hints.get("load_sensitive", True)
            msg.routing_hints.alternative_channels = hints.get("alternative_channels", [])
            msg.routing_hints.strategy = hints.get("strategy", "fastest")

        # Restore dual representation (optional)
        if "wave_state" in data and data["wave_state"] is not None:
            try:
                if WaveState is not type(None):
                    msg.wave_state = WaveState(**data["wave_state"])
            except Exception:
                logger.debug("Failed to hydrate WaveState from persisted data – keeping None.")

        if "particle_state" in data and data["particle_state"] is not None:
            try:
                if ParticleState is not type(None):
                    msg.particle_state = ParticleState(**data["particle_state"])
            except Exception:
                logger.debug("Failed to hydrate ParticleState from persisted data – keeping None.")

        msg.provenance_hash = data.get("provenance_hash")

        # ------------------------------------------------------------------
        # Restore tracing context (NEW)
        # ------------------------------------------------------------------
        msg.trace_id = data.get("trace_id", str(uuid.uuid4()))
        msg.span_id = data.get("span_id", uuid.uuid4().hex[:16])
        msg.parent_span_id = data.get("parent_span_id")
        msg.trace_flags = data.get("trace_flags", 0)
        msg.trace_state = data.get("trace_state", {})
        msg.caw_branch_taken = data.get("caw_branch_taken")

        # Restore capability token (auto-decode if base64 string was persisted)
        cap_token = data.get("capability_token")
        if isinstance(cap_token, str):
            try:
                msg.capability_token = base64.b64decode(cap_token)
            except Exception:
                msg.capability_token = cap_token
        else:
            msg.capability_token = cap_token

        return msg

    @property
    def priority_int(self) -> int:
        """Directly return the integer priority."""
        return self.priority

    # --------------------------- helper methods ---------------------------
    def set_capability_token(self, token: Union[str, bytes]) -> None:  # noqa: D401
        """Setter that handles automatic base64 decoding for hex/base64 strings."""
        if isinstance(token, str):
            # Heuristic: if token length divisible by 4 and only base64 chars -> decode
            if (
                all(
                    c in "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
                    for c in token
                )
                and len(token) % 4 == 0
            ):
                try:
                    self.capability_token = base64.b64decode(token)
                    return
                except Exception:
                    pass
        self.capability_token = token

    @property
    def biscuit_token(self) -> Optional[bytes]:  # noqa: D401
        """Return Biscuit token bytes if available, else None."""
        return (
            self.capability_token if isinstance(self.capability_token, (bytes, bytearray)) else None
        )


# ============================================================================
# Channel Subscription
# ============================================================================


@dataclass
class ChannelSubscription:
    """Represents a subscription to a channel pattern.

    Note: The 'priority' field here is for handler ordering (which handler
    gets called first when multiple handlers match), NOT message priority
    (which messages get processed first from the queue).
    """

    subscriber_id: str
    channel_pattern: str  # Can use wildcards: "pc.memory.*"
    handler: Callable[[HybridMessage], Any]
    priority: int = 0  # Higher priority handlers get messages first (handler priority)
    filter_func: Optional[Callable[[HybridMessage], bool]] = None

    # Capability token presented by the subscriber.  When the bus routes a
    # message whose channel requires a specific capability (as declared in
    # ChannelRegistry.min_capability), the dispatcher will deliver the
    # message only to subscriptions whose ``capability_token`` satisfies the
    # requirement.  ``None`` means the subscriber has no capability and will
    # only receive messages from channels that do not enforce one.
    capability_token: Optional[str] = None

    # Subscription options
    exclusive: bool = False  # Only this handler gets messages?
    durable: bool = True  # Survive restarts?

    def matches(self, channel: str) -> bool:
        """Check if a channel matches this subscription pattern."""
        # Fast-path: exact match or single-segment wildcard
        if self.channel_pattern in {"*", channel}:
            return True

        pattern_parts: list[str] = self.channel_pattern.split(".")
        channel_parts: list[str] = channel.split(".")

        i = 0
        while i < len(pattern_parts):
            part = pattern_parts[i]

            if part == "#":
                # Multi-segment wildcard – always matches remaining segments
                return True
            if part == "*":
                # Single-segment wildcard – skip exactly one channel segment
                i += 1
                if i >= len(channel_parts):
                    # Pattern requires more segments than channel provides
                    return False
                if channel_parts:
                    channel_parts.pop(0)
                continue

            # Concrete segment – must match exactly
            if not channel_parts or part != channel_parts[0]:
                return False

            # Advance to next segment
            if channel_parts:
                channel_parts.pop(0)
            i += 1

        # If pattern fully processed, ensure no leftover channel segments unless pattern ended with '#'
        return not channel_parts or pattern_parts[-1] == "#"

    def should_handle(self, message: HybridMessage) -> bool:
        """Check if this subscription should handle the message."""
        if not self.matches(message.channel):
            return False

        if self.filter_func and not self.filter_func(message):
            return False

        return True


# ============================================================================
# Message Processing Result
# ============================================================================


@dataclass
class MessageResult:
    """Result of processing a message."""

    success: bool
    message_id: str
    handler_id: str
    processing_time_ms: float

    # Result data
    response: Optional[HybridMessage] = None
    error: Optional[str] = None

    # Metrics
    fidelity_used: float = 1.0
    resources_consumed: Dict[str, float] = field(default_factory=dict)

    def to_metrics(self) -> Dict[str, Any]:
        """Convert to metrics for monitoring."""
        return {
            "success": self.success,
            "processing_time_ms": self.processing_time_ms,
            "fidelity_used": self.fidelity_used,
            "error": self.error is not None,
            **self.resources_consumed,
        }


# ==========================================================================
# Helper class for flexible message_type comparisons
# ==========================================================================


class MessageTypeStr(str):
    """A string subclass that compares equal to Enum constants if names/values match."""

    def __eq__(self, other):  # type: ignore[override]
        if isinstance(other, Enum):
            other_val = getattr(other, "value", None)
            return str(self) == str(other_val) or str(self) == other.name
        return str(self) == str(other)

    def __hash__(self):  # Needed because __eq__ overridden
        return str.__hash__(self)


# =========================================================================
# Envelope Validator (Sprint-1 addition)
# =========================================================================

_REQUIRED_ENVELOPE_FIELDS = [
    "channel",
    "payload",
    "acf_metadata",
    "routing_hints",
    "execution_constraints",
    "context",
    "priority",
    "wave_particle_ratio",
    "wave_state",
    "particle_state",
]


def validate(message: "HybridMessage") -> None:  # noqa: D401
    """Validate that a HybridMessage contains all mandatory fields.

    Raises:
        ValueError: If any required field is missing or malformed.
    """
    missing: List[str] = []
    for field_name in _REQUIRED_ENVELOPE_FIELDS:
        if getattr(message, field_name, None) is None:
            missing.append(field_name)
    if missing:
        raise ValueError(f"Invalid HybridMessage – missing fields: {', '.join(missing)}")

    # Minimal type sanity checks (lightweight – not full schema validation)
    if not isinstance(message.channel, str) or message.channel == "":
        raise ValueError("HybridMessage.channel must be non-empty string")
    if not isinstance(message.payload, dict):
        raise ValueError("HybridMessage.payload must be a dict")
    if not hasattr(message, "priority") or not 0 <= message.priority <= SCALE:
        raise ValueError(f"HybridMessage.priority must be between 0 and {SCALE}")
    if not 0.0 <= message.wave_particle_ratio <= 1.0:
        raise ValueError("HybridMessage.wave_particle_ratio must be between 0.0 and 1.0")

    if message.wave_state is None or message.particle_state is None:
        raise ValueError("HybridMessage must include both wave_state and particle_state objects")


def create_message(
    channel: str,
    context: "UnifiedContext",
    message_type: MessageType = MessageType.EVENT,
    payload: Optional[Dict[str, Any]] = None,
    priority: Optional[int] = None,
    **kwargs
) -> HybridMessage:
    """
    Create a HybridMessage with mandatory context propagation.

    This function enforces Principle II (Contextual Supremacy) by requiring
    a UnifiedContext for all message creation operations.

    Args:
        channel: Message channel
        context: UnifiedContext for the message (REQUIRED)
        message_type: Type of message (default: EVENT)
        payload: Message payload
        priority: Message priority (defaults to context priority)
        **kwargs: Additional message parameters

    Returns:
        HybridMessage with proper context propagation

    Raises:
        ValueError: If context is None
    """
    if context is None:
        raise ValueError("context is required for message creation (Principle II)")

    # Use context priority if not explicitly provided
    if priority is None:
        priority = getattr(context, 'priority', PRIO_NORMAL)

    # Ensure context is properly serialized
    context_dict = context.to_dict() if hasattr(context, 'to_dict') else {}

    # Create message with context propagation
    message = HybridMessage(
        channel=channel,
        message_type=message_type,
        payload=payload or {},
        priority=priority,
        context=context_dict,
        correlation_id=context.context_id,
        wave_particle_ratio=getattr(context, 'wave_particle_ratio', 0.5),
        **kwargs
    )

    # Add context metadata to causality chain
    message.causality_chain.append(context.context_id)

    return message


__all__ = [
    # existing exports … trimmed for brevity
    "HybridMessage",
    "ChannelSubscription",
    "MessageResult",
    "validate",
    "create_message",
]


@dataclass
class WaveState:  # noqa: D101 – minimal stub
    representation: str = "wave"
    metadata: dict = field(default_factory=dict)


@dataclass
class ParticleState:  # noqa: D101 – minimal stub
    representation: str = "particle"
    metadata: dict = field(default_factory=dict)
