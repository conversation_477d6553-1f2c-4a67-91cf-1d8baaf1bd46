"""
Connection Pooling for Storage Backends
====================================

This module provides connection pooling for storage backends, allowing efficient
reuse of database connections and other resources.

Key Features:
- Generic connection pool for database connections
- SQLite connection pool with optimized settings
- Automatic connection management and cleanup
- Optimized for M3 Max hardware

Related Files:
- storage_backends.py: Storage backend implementations
- tiered_storage.py: Tiered storage system
- performance.py: Performance monitoring utilities

Dependencies:
- Python 3.8+
- aiosqlite>=0.17.0: For asynchronous SQLite operations
"""

import asyncio
import logging
import time
from typing import Any
from typing import Callable
from typing import Dict
from typing import Generic
from typing import List
from typing import Optional
from typing import Set
from typing import Tuple
from typing import TypeVar

# Configure logger
logger = logging.getLogger(
    "person_suit.core.infrastructure.dual_wave.persistence.connection_pool"
)

# Type variables
T = TypeVar("T")  # Generic type for connections


class ConnectionPool(Generic[T]):
    """
    Generic connection pool for database connections and other resources.

    This class provides a pool of connections that can be acquired and released,
    with automatic creation and cleanup of connections as needed.
    """

    def __init__(
        self,
        create_connection: Callable[[], asyncio.Future[T]],
        close_connection: Callable[[T], asyncio.Future[None]],
        max_connections: int = 10,
        max_idle_time: float = 60.0,
        connection_timeout: float = 30.0,
    ):
        """
        Initialize the connection pool.

        Args:
            create_connection: Function to create a new connection
            close_connection: Function to close a connection
            max_connections: Maximum number of connections in the pool
            max_idle_time: Maximum time in seconds a connection can be idle before being closed
            connection_timeout: Timeout in seconds for acquiring a connection
        """
        self.create_connection = create_connection
        self.close_connection = close_connection
        self.max_connections = max_connections
        self.max_idle_time = max_idle_time
        self.connection_timeout = connection_timeout

        # Connection pool
        self.pool: List[Tuple[T, float]] = []  # (connection, last_used_time)
        self.in_use: Set[T] = set()

        # Semaphore to limit concurrent connections
        self.semaphore = asyncio.Semaphore(max_connections)

        # Cleanup task
        self.cleanup_task = None
        self.running = False

    async def start(self):
        """Start the connection pool and cleanup task."""
        self.running = True
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())

    async def stop(self):
        """Stop the connection pool and cleanup task."""
        self.running = False
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass

        # Close all connections
        for conn, _ in self.pool:
            await self.close_connection(conn)

        for conn in self.in_use:
            await self.close_connection(conn)

        self.pool.clear()
        self.in_use.clear()

    async def acquire(self) -> T:
        """
        Acquire a connection from the pool.

        Returns:
            A connection from the pool

        Raises:
            asyncio.TimeoutError: If a connection could not be acquired within the timeout
        """
        # Try to get a connection from the pool
        async with asyncio.timeout(self.connection_timeout):
            await self.semaphore.acquire()

            try:
                # Try to get an idle connection
                if self.pool:
                    conn, _ = self.pool.pop(0)
                    self.in_use.add(conn)
                    return conn

                # Create a new connection
                conn = await self.create_connection()
                self.in_use.add(conn)
                return conn
            except Exception:
                # Release semaphore on error
                self.semaphore.release()
                raise

    async def release(self, conn: T):
        """
        Release a connection back to the pool.

        Args:
            conn: The connection to release
        """
        if conn in self.in_use:
            self.in_use.remove(conn)
            self.pool.append((conn, time.time()))
            self.semaphore.release()

    async def _cleanup_loop(self):
        """Periodically clean up idle connections."""
        while self.running:
            try:
                await asyncio.sleep(self.max_idle_time / 2)
                await self._cleanup_idle_connections()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in connection pool cleanup: {str(e)}")

    async def _cleanup_idle_connections(self):
        """Clean up idle connections that have exceeded the maximum idle time."""
        now = time.time()
        idle_cutoff = now - self.max_idle_time

        # Find idle connections to close
        to_close = []
        remaining = []

        for conn, last_used in self.pool:
            if last_used < idle_cutoff:
                to_close.append(conn)
            else:
                remaining.append((conn, last_used))

        # Update pool
        self.pool = remaining

        # Close idle connections
        for conn in to_close:
            try:
                await self.close_connection(conn)
            except Exception as e:
                logger.error(f"Error closing idle connection: {str(e)}")


class SQLiteConnectionPool:
    """
    Connection pool for SQLite connections.

    This class provides a pool of SQLite connections with optimized settings
    for the Person Suit framework.
    """

    def __init__(
        self,
        db_path: str,
        max_connections: int = 5,
        max_idle_time: float = 60.0,
        connection_timeout: float = 30.0,
        pragmas: Optional[Dict[str, Any]] = None,
    ):
        """
        Initialize the SQLite connection pool.

        Args:
            db_path: Path to the SQLite database file
            max_connections: Maximum number of connections in the pool
            max_idle_time: Maximum time in seconds a connection can be idle before being closed
            connection_timeout: Timeout in seconds for acquiring a connection
            pragmas: Dictionary of SQLite PRAGMA statements to execute on new connections
        """
        self.db_path = db_path
        self.pragmas = pragmas or {
            "journal_mode": "WAL",
            "synchronous": "NORMAL",
            "cache_size": 10000,
            "foreign_keys": "ON",
            "temp_store": "MEMORY",
        }

        # Create connection pool
        self.pool = ConnectionPool(
            create_connection=self._create_connection,
            close_connection=self._close_connection,
            max_connections=max_connections,
            max_idle_time=max_idle_time,
            connection_timeout=connection_timeout,
        )

    async def start(self):
        """Start the connection pool."""
        await self.pool.start()

    async def stop(self):
        """Stop the connection pool."""
        await self.pool.stop()

    async def _create_connection(self):
        """
        Create a new SQLite connection.

        Returns:
            A new SQLite connection
        """
        import aiosqlite

        conn = await aiosqlite.connect(self.db_path)

        # Set pragmas
        for pragma, value in self.pragmas.items():
            await conn.execute(f"PRAGMA {pragma} = {value}")

        # Set busy timeout
        await conn.execute("PRAGMA busy_timeout = 5000")

        return conn

    async def _close_connection(self, conn):
        """
        Close a SQLite connection.

        Args:
            conn: The connection to close
        """
        await conn.close()

    async def acquire(self):
        """
        Acquire a connection from the pool.

        Returns:
            A connection from the pool
        """
        return await self.pool.acquire()

    async def release(self, conn):
        """
        Release a connection back to the pool.

        Args:
            conn: The connection to release
        """
        await self.pool.release(conn)

    def create_execute_effect(self, sql, parameters=None):
        """
        Create a database execution effect instead of performing direct I/O.

        This follows Principle I (Absolute Decoupling through Choreographed Effects)
        by returning a declarative Effect instead of performing database operations.

        Args:
            sql: SQL statement to execute
            parameters: Parameters for the SQL statement

        Returns:
            DatabaseEffect describing the intended operation
        """
        from person_suit.core.effects.database import DatabaseEffect

        return DatabaseEffect(
            operation="execute",
            query=sql,
            parameters=parameters or (),
            required_capability="database:execute",
            metadata={"connection_pool": "sqlite", "pool_id": id(self)}
        )

    async def execute(self, sql, parameters=None):
        """
        DEPRECATED: Execute a SQL statement directly.

        This method violates Principle I and will be removed.
        Use create_execute_effect() and execute through EffectInterpreter instead.

        Args:
            sql: SQL statement to execute
            parameters: Parameters for the SQL statement

        Returns:
            Result of the execution
        """
        import warnings
        warnings.warn(
            "Direct database execution is deprecated. Use create_execute_effect() instead.",
            DeprecationWarning,
            stacklevel=2
        )

        conn = await self.acquire()
        try:
            return await conn.execute(sql, parameters or ())
        finally:
            await self.release(conn)

    async def execute_many(self, sql, parameters_list):
        """
        Execute a SQL statement with multiple parameter sets.

        Args:
            sql: SQL statement to execute
            parameters_list: List of parameter sets

        Returns:
            Result of the execution
        """
        conn = await self.acquire()
        try:
            return await conn.executemany(sql, parameters_list)
        finally:
            await self.release(conn)

    async def execute_fetchall(self, sql, parameters=None):
        """
        Execute a SQL statement and fetch all results.

        Args:
            sql: SQL statement to execute
            parameters: Parameters for the SQL statement

        Returns:
            All rows from the result set
        """
        conn = await self.acquire()
        try:
            cursor = await conn.execute(sql, parameters or ())
            return await cursor.fetchall()
        finally:
            await self.release(conn)

    async def execute_fetchone(self, sql, parameters=None):
        """
        Execute a SQL statement and fetch one result.

        Args:
            sql: SQL statement to execute
            parameters: Parameters for the SQL statement

        Returns:
            First row from the result set, or None if no rows
        """
        conn = await self.acquire()
        try:
            cursor = await conn.execute(sql, parameters or ())
            return await cursor.fetchone()
        finally:
            await self.release(conn)

    async def transaction(self):
        """
        Start a transaction.

        Returns:
            A transaction context manager
        """
        return _Transaction(self)


class _Transaction:
    """
    Transaction context manager for SQLite connection pool.

    This class provides a context manager for executing multiple SQL statements
    in a transaction, with automatic commit or rollback.
    """

    def __init__(self, pool: SQLiteConnectionPool):
        """
        Initialize the transaction.

        Args:
            pool: The SQLite connection pool
        """
        self.pool = pool
        self.conn = None

    async def __aenter__(self):
        """
        Enter the transaction context.

        Returns:
            The SQLite connection
        """
        self.conn = await self.pool.acquire()
        await self.conn.execute("BEGIN TRANSACTION")
        return self.conn

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """
        Exit the transaction context.

        Args:
            exc_type: Exception type, if an exception was raised
            exc_val: Exception value, if an exception was raised
            exc_tb: Exception traceback, if an exception was raised
        """
        try:
            if exc_type is None:
                # No exception, commit the transaction
                await self.conn.commit()
            else:
                # Exception, rollback the transaction
                await self.conn.rollback()
        finally:
            # Release the connection back to the pool
            await self.pool.release(self.conn)
            self.conn = None
