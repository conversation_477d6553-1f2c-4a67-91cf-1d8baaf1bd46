"""
API Server for Distributed Storage (CAW-Aligned)
===============================================

Purpose:
    Provides an HTTP API server for the distributed storage system, enabling
    external applications to interact with distributed, context-aware storage
    in alignment with Contextual Adaptive Wave Programming (CAW) principles.

This module exposes endpoints for storing, retrieving, and managing items in a
distributed, dual-wave storage backend. It supports context propagation,
adaptive computation, and secure, observable interactions, facilitating
integration with CAW-based systems.

Key Features:
- HTTP API for distributed, context-aware storage operations
- JSON-based API for storing and retrieving items
- Authentication, authorization, and capability-based access (future CAW extension)
- Rate limiting, throttling, and adaptive resource management
- Metrics and monitoring for system introspection and adaptive fidelity

Related Files:
- distributed/config.py: Configuration for distributed storage
- distributed/node.py: Node discovery and management
- distributed/dht.py: Distributed hash table for data partitioning
- distributed/backend.py: Distributed storage backend implementation

Dependencies:
- Python 3.8+
- aiohttp>=3.7.0: For HTTP server
"""

import base64
import json
import logging
import pickle
import time
from typing import Any
from typing import TypeVar

from aiohttp import web

from ...core import DualInformation
from ...core import ParticleState
from ...core import WaveState
from .backend import DistributedStorageBackend
from .config import DistributedStorageConfig

# Configure logger
logger = logging.getLogger(
    "person_suit.core.infrastructure.dual_wave.persistence.distributed.api_server"
)

# Type variables
T = TypeVar("T")  # Generic type for stored items


class APIServer:
    """
    HTTP API server for distributed, context-aware storage (CAW-aligned).

    Exposes endpoints for interacting with the distributed storage backend,
    supporting context propagation, duality (wave/particle state), and adaptive
    computation as defined by CAW principles.

    Enables secure, observable, and adaptive access to distributed storage,
    facilitating integration with CAW-based and dual-wave systems.

    Attributes:
        config: DistributedStorageConfig instance for system configuration.
        backend: DistributedStorageBackend instance for storage operations.
        host: Host address for the API server.
        port: Port for the API server.
    """

    def __init__(
        self,
        config: DistributedStorageConfig,
        backend: DistributedStorageBackend,
        host: str = None,
        port: int = None,
    ):
        """
        Initialize the API server.

        Args:
            config: Configuration for the distributed storage system
            backend: Distributed storage backend
            host: Host to listen on (defaults to the local node's host)
            port: Port to listen on (defaults to the local node's port + 1)
        """
        self.config = config
        self.backend = backend

        # Set host and port
        self.host = host if host is not None else config.local_node.host
        self.port = port if port is not None else config.local_node.port + 1

        # Initialize server
        self.app = None
        self.runner = None
        self.site = None

    def create_server_start_effect(self):
        """
        Create an effect to start the HTTP API server.

        This follows Principle I (Absolute Decoupling through Choreographed Effects)
        by returning a declarative Effect instead of performing network operations.

        Returns:
            NetworkEffect describing the intended server start operation
        """
        from person_suit.core.effects.network import NetworkEffect

        return NetworkEffect(
            operation="start_server",
            url=f"http://{self.host}:{self.port}",
            required_capability="network:server:start",
            metadata={
                "server_type": "http_api",
                "host": self.host,
                "port": self.port,
                "endpoints": ["/", "/api/info", "/api/wave", "/api/particle"]
            }
        )

    async def start(self):
        """
        DEPRECATED: Start the HTTP API server directly.

        This method violates Principle I and will be removed.
        Use create_server_start_effect() and execute through EffectInterpreter instead.

        Initializes the aiohttp application, registers all endpoints, and
        begins listening for incoming requests. Supports context propagation
        and adaptive resource management in line with CAW principles.

        Raises:
            Exception: If the server fails to start.
        """
        import warnings
        warnings.warn(
            "Direct server start is deprecated. Use create_server_start_effect() instead.",
            DeprecationWarning,
            stacklevel=2
        )

        # Create application
        self.app = web.Application()

        # Add routes
        self.app.router.add_get("/", self._handle_root)
        self.app.router.add_get("/api/info", self._handle_info)
        self.app.router.add_get("/api/items", self._handle_list_items)
        self.app.router.add_get("/api/items/{item_id}", self._handle_get_item)
        self.app.router.add_post("/api/items", self._handle_store_item)
        self.app.router.add_delete("/api/items/{item_id}", self._handle_delete_item)
        self.app.router.add_head("/api/items/{item_id}", self._handle_exists_item)
        self.app.router.add_get("/api/metrics", self._handle_metrics)

        # Start server
        self.runner = web.AppRunner(self.app)
        await self.runner.setup()

        self.site = web.TCPSite(self.runner, self.host, self.port)
        await self.site.start()

        logger.info(f"API server started on {self.host}:{self.port}")

    async def stop(self):
        """
        Stop the HTTP API server.

        Gracefully shuts down the server and releases all resources, ensuring
        proper context closure and system stability.
        """
        if self.site:
            await self.site.stop()

        if self.runner:
            await self.runner.cleanup()

        logger.info("API server stopped")

    async def _handle_root(self, request):
        """
        Handle a request to the root endpoint.

        Provides basic API and node metadata, supporting context-aware
        introspection and system discovery in CAW-based distributed systems.

        Args:
            request: HTTP request

        Returns:
            HTTP response with basic information
        """
        return web.json_response(
            {
                "name": "Distributed Storage API",
                "version": "1.0.0",
                "node_id": self.config.local_node.node_id,
            }
        )

    async def _handle_info(self, request):
        """
        Handle a request for system information.

        Returns system and node metadata, supporting context-aware introspection
        and adaptive management in distributed CAW systems.

        Args:
            request: HTTP request

        Returns:
            HTTP response with system information
        """
        # Get active nodes
        active_nodes = self.backend.node_manager.get_active_nodes()

        # Prepare response
        response_data = {
            "node_id": self.config.local_node.node_id,
            "node_count": len(active_nodes),
            "nodes": [
                {
                    "node_id": node_id,
                    "host": node_info.config.host,
                    "port": node_info.config.port,
                    "is_seed_node": node_info.config.is_seed_node,
                    "status": node_info.status.value,
                }
                for node_id, node_info in active_nodes.items()
            ],
            "replication_factor": self.config.replication.factor,
            "replication_strategy": self.config.replication.strategy.value,
            "partitioning_strategy": self.config.partitioning.strategy.value,
        }

        return web.json_response(response_data)

    async def _handle_list_items(self, request):
        """
        Handle a request to list items.

        Lists all items in the distributed storage, supporting context-aware
        data discovery and adaptive computation.

        Args:
            request: HTTP request

        Returns:
            HTTP response with list of items
        """
        try:
            # List items
            items = await self.backend.list_items()

            # Prepare response
            response_data = {"items": items, "count": len(items)}

            return web.json_response(response_data)
        except Exception as e:
            logger.error(f"Error listing items: {str(e)}")
            return web.json_response({"error": str(e)}, status=500)

    async def _handle_get_item(self, request):
        """
        Handle a request to get an item.

        Retrieves an item from distributed storage, supporting context propagation
        and duality (wave/particle state) in CAW systems.

        Args:
            request: HTTP request

        Returns:
            HTTP response with the item
        """
        try:
            # Get item ID from URL
            item_id = request.match_info["item_id"]

            # Retrieve item
            item = await self.backend.retrieve(item_id)

            if item is None:
                return web.json_response(
                    {"error": f"Item {item_id} not found"}, status=404
                )

            # Serialize item
            serialized_item = self._serialize_item(item)

            # Prepare response
            response_data = {
                "item_id": item_id,
                "item_type": type(item).__name__,
                "item_data": serialized_item,
            }

            return web.json_response(response_data)
        except Exception as e:
            logger.error(f"Error getting item: {str(e)}")
            return web.json_response({"error": str(e)}, status=500)

    async def _handle_store_item(self, request):
        """
        Handle a request to store an item.

        Stores an item in distributed storage, supporting context propagation
        and adaptive computation in CAW-based systems.

        Args:
            request: HTTP request

        Returns:
            HTTP response with the result
        """
        try:
            # Parse request
            data = await request.json()

            # Get item ID and data
            item_id = data.get("item_id")
            item_type = data.get("item_type")
            item_data = data.get("item_data")

            if not item_id or not item_type or not item_data:
                return web.json_response(
                    {"error": "Missing required fields: item_id, item_type, item_data"},
                    status=400,
                )

            # Deserialize item
            item = self._deserialize_item(item_data, item_type)

            if item is None:
                return web.json_response(
                    {"error": f"Failed to deserialize item of type {item_type}"},
                    status=400,
                )

            # Store item
            success = await self.backend.store(item_id, item)

            # Prepare response
            response_data = {"success": success, "item_id": item_id}

            return web.json_response(response_data)
        except Exception as e:
            logger.error(f"Error storing item: {str(e)}")
            return web.json_response({"error": str(e)}, status=500)

    async def _handle_delete_item(self, request):
        """
        Handle a request to delete an item.

        Deletes an item from distributed storage, supporting context-aware
        resource management and adaptive computation.

        Args:
            request: HTTP request

        Returns:
            HTTP response with the result
        """
        try:
            # Get item ID from URL
            item_id = request.match_info["item_id"]

            # Delete item
            success = await self.backend.delete(item_id)

            # Prepare response
            response_data = {"success": success, "item_id": item_id}

            return web.json_response(response_data)
        except Exception as e:
            logger.error(f"Error deleting item: {str(e)}")
            return web.json_response({"error": str(e)}, status=500)

    async def _handle_exists_item(self, request):
        """
        Handle a request to check if an item exists.

        Checks for the existence of an item in distributed storage, supporting
        context-aware data management and adaptive computation.

        Args:
            request: HTTP request

        Returns:
            HTTP response with the result
        """
        try:
            # Get item ID from URL
            item_id = request.match_info["item_id"]

            # Check if item exists
            exists = await self.backend.exists(item_id)

            if exists:
                return web.Response(status=200)
            else:
                return web.Response(status=404)
        except Exception as e:
            logger.error(f"Error checking if item exists: {str(e)}")
            return web.Response(status=500)

    async def _handle_metrics(self, request):
        """
        Handle a request for metrics.

        Returns system and storage metrics, supporting adaptive computational
        fidelity and introspection in CAW-based distributed systems.

        Args:
            request: HTTP request

        Returns:
            HTTP response with metrics
        """
        try:
            # Get metrics
            item_count = await self.backend.count_items()
            total_size = await self.backend.get_size()

            # Get active nodes
            active_nodes = self.backend.node_manager.get_active_nodes()

            # Prepare response
            response_data = {
                "item_count": item_count,
                "total_size_bytes": total_size,
                "node_count": len(active_nodes),
                "timestamp": time.time(),
            }

            return web.json_response(response_data)
        except Exception as e:
            logger.error(f"Error getting metrics: {str(e)}")
            return web.json_response({"error": str(e)}, status=500)

    def _serialize_item(self, item: Any) -> str:
        """
        Serialize an item to a string.

        Serializes items for storage or transmission, supporting duality
        (wave/particle state) and context propagation in CAW systems.

        Args:
            item: The item to serialize

        Returns:
            Serialized item as a string
        """
        if isinstance(item, (WaveState, ParticleState, DualInformation)):
            # Use pickle for complex objects
            serialized = pickle.dumps(item)
            return base64.b64encode(serialized).decode("ascii")
        else:
            # Use JSON for simple objects
            return json.dumps(item)

    def _deserialize_item(self, serialized: str, item_type: str) -> Any:
        """
        Deserialize an item from a string.

        Deserializes items for use in the system, supporting duality
        (wave/particle state) and context propagation in CAW systems.

        Args:
            serialized: Serialized item as a string
            item_type: Type of the item

        Returns:
            Deserialized item
        """
        try:
            # For complex objects
            if item_type in ["WaveState", "ParticleState", "DualInformation"]:
                serialized_bytes = base64.b64decode(serialized.encode("ascii"))
                return pickle.loads(serialized_bytes)
            else:
                # For simple objects
                return json.loads(serialized)
        except Exception as e:
            logger.error(f"Failed to deserialize item of type {item_type}: {str(e)}")
            return None
