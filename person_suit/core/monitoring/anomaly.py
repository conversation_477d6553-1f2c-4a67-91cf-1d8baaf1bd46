"""
Person Suit - Monitoring System Anomaly Detection (IC-2)

This module provides anomaly detection capabilities for the Person Suit monitoring system.
It identifies unusual patterns, outliers, and abnormal behavior in metrics and system data.

Components:
- Detection Methods: Different anomaly detection algorithms
- Anomaly Types: Classification of different types of anomalies
- Detectors: Components that analyze data for anomalies
- Notification: Mechanisms for reporting detected anomalies

Note: Detailed detector implementations are in separate modules:
- statistical_detector.py: Statistical anomaly detection
- rule_detector.py: Rule-based anomaly detection
"""

import logging
import asyncio
import time
import uuid
from dataclasses import dataclass
from dataclasses import field
from enum import Enum
from enum import auto

# ------------------------------------------------------------------------------
# Imports and Setup
# ------------------------------------------------------------------------------
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

# Import from other monitoring modules
from person_suit.core.constants.fixed_point_scale import (
    SCALE, PRIO_NORMAL, PRIO_HIGH, PRIO_CRITICAL,
    float_to_bucket, bucket_to_float
)

# Setup logging
logger = logging.getLogger(__name__)

# ------------------------------------------------------------------------------
# Anomaly Types and Constants
# ------------------------------------------------------------------------------

# SCALE-based anomaly detection thresholds (using centralized configuration)
ANOMALY_Z_SCORE_THRESHOLD = float_to_bucket(3.0)  # 3.0 standard deviations
ANOMALY_IQR_MULTIPLIER = float_to_bucket(1.5)     # 1.5 * IQR for outlier detection
ANOMALY_MIN_SEVERITY = float_to_bucket(0.5)       # Minimum severity to notify (50%)
ANOMALY_TREND_WINDOW = 20                         # Number of points for trend detection
ANOMALY_TRAINING_WINDOW = 1000                    # ML training window size
ANOMALY_RETRAINING_INTERVAL = 3600                # Retraining interval in seconds
ANOMALY_COOLDOWN_PERIOD = 300                     # Cooldown period in seconds
ANOMALY_DETECTION_INTERVAL = 60                   # Detection interval in seconds
ANOMALY_LOOKBACK_WINDOW = 100                     # Lookback window size


class AnomalyType(Enum):
    """Defines the types of anomalies that can be detected."""

    SPIKE = auto()  # Sudden increase above normal levels
    DROP = auto()  # Sudden decrease below normal levels
    TREND = auto()  # Gradual increase or decrease over time
    LEVEL_SHIFT = auto()  # Permanent change in the average value
    OUTLIER = auto()  # Individual data point outside normal range
    PATTERN_CHANGE = auto()  # Change in the pattern of data
    VARIANCE_CHANGE = auto()  # Change in the variability of data


class DetectionMethod(Enum):
    """Defines the methods used for anomaly detection."""

    STATISTICAL = auto()  # Statistical methods (e.g., z-score, IQR)
    MACHINE_LEARNING = auto()  # Machine learning methods
    RULE_BASED = auto()  # Rule-based methods (e.g., thresholds)
    FORECASTING = auto()  # Forecast-based methods


@dataclass
class Anomaly:
    """
    Represents a detected anomaly.

    Contains information about when, where, and what type of anomaly was detected.
    """

    anomaly_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    metric_name: str = ""
    metric_namespace: str = ""
    anomaly_type: AnomalyType = AnomalyType.OUTLIER
    detection_method: DetectionMethod = DetectionMethod.STATISTICAL
    timestamp: float = field(default_factory=time.time)
    severity: float = 0.0  # 0.0 to 1.0, higher means more severe
    description: str = ""
    detected_value: Any = None
    expected_value: Any = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the anomaly to a dictionary representation.

        Returns:
            Dictionary representation of the anomaly
        """
        return {
            "anomaly_id": self.anomaly_id,
            "metric_name": self.metric_name,
            "metric_namespace": self.metric_namespace,
            "anomaly_type": self.anomaly_type.name,
            "detection_method": self.detection_method.name,
            "timestamp": self.timestamp,
            "severity": self.severity,
            "description": self.description,
            "detected_value": self.detected_value,
            "expected_value": self.expected_value,
            "metadata": self.metadata,
        }


# ------------------------------------------------------------------------------
# Detection Configuration
# ------------------------------------------------------------------------------


@dataclass
class DetectionConfig:
    """
    Configuration for anomaly detection.

    Defines parameters and settings for anomaly detection algorithms.
    """

    # General settings
    enabled: bool = True
    detection_interval: float = ANOMALY_DETECTION_INTERVAL  # Use centralized constant
    lookback_window: int = ANOMALY_LOOKBACK_WINDOW  # Use centralized constant

    # Statistical detection settings (using SCALE-based thresholds)
    z_score_threshold: int = ANOMALY_Z_SCORE_THRESHOLD  # SCALE bucket for 3.0 std devs
    iqr_multiplier: int = ANOMALY_IQR_MULTIPLIER  # SCALE bucket for 1.5 multiplier
    trend_detection_window: int = ANOMALY_TREND_WINDOW  # Use centralized constant

    # Rule-based settings
    min_threshold: Optional[float] = None
    max_threshold: Optional[float] = None

    # Machine learning settings
    training_window: int = ANOMALY_TRAINING_WINDOW  # Use centralized constant
    retraining_interval: float = ANOMALY_RETRAINING_INTERVAL  # Use centralized constant

    # Notification settings (using SCALE-based thresholds)
    min_severity_to_notify: int = ANOMALY_MIN_SEVERITY  # SCALE bucket for 50% severity
    cooldown_period: float = ANOMALY_COOLDOWN_PERIOD  # Use centralized constant


# ------------------------------------------------------------------------------
# Detector Base Class
# ------------------------------------------------------------------------------


class AnomalyDetector:
    """
    Base class for anomaly detectors.

    Provides common functionality for anomaly detection.
    """

    def __init__(self, config: Optional[DetectionConfig] = None):
        """
        Initialize an anomaly detector.

        Args:
            config: Configuration for the detector
        """
        self.config = config or DetectionConfig()
        self._last_anomalies: Dict[
            str, Dict[AnomalyType, Anomaly]
        ] = {}  # metric_id -> {anomaly_type -> anomaly}
        self._last_anomaly_times: Dict[
            str, Dict[AnomalyType, float]
        ] = {}  # metric_id -> {anomaly_type -> timestamp}
        self._lock = asyncio.Lock()

    def detect(self, metric_name: str, metric_namespace: str, values: List[float]) -> List[Anomaly]:
        """
        Detect anomalies in a series of values.

        This method should be implemented by subclasses to perform specific
        anomaly detection algorithms.

        Args:
            metric_name: Name of the metric
            metric_namespace: Namespace of the metric
            values: Time series of values to analyze

        Returns:
            List of detected anomalies
        """
        raise NotImplementedError("Subclasses must implement detect()")

    def _should_notify(self, metric_id: str, anomaly_type: AnomalyType, anomaly: Anomaly) -> bool:
        """
        Check if an anomaly should trigger a notification.

        Implements cooldown logic to prevent notification flooding.

        Args:
            metric_id: ID of the metric
            anomaly_type: Type of anomaly
            anomaly: Detected anomaly

        Returns:
            True if notification should be sent, False otherwise
        """
        with self._lock:
            # Check severity threshold (convert SCALE bucket to float for comparison)
            min_severity_float = bucket_to_float(self.config.min_severity_to_notify)
            if anomaly.severity < min_severity_float:
                return False

            # Check cooldown
            now = time.time()
            last_time = self._last_anomaly_times.get(metric_id, {}).get(anomaly_type, 0)

            if now - last_time < self.config.cooldown_period:
                # Still in cooldown period
                return False

            # Update last anomaly time
            if metric_id not in self._last_anomaly_times:
                self._last_anomaly_times[metric_id] = {}
            self._last_anomaly_times[metric_id][anomaly_type] = now

            # Update last anomaly
            if metric_id not in self._last_anomalies:
                self._last_anomalies[metric_id] = {}
            self._last_anomalies[metric_id][anomaly_type] = anomaly

            return True

    def _calculate_severity(self, value: float, expected: float, threshold: float) -> float:
        """
        Calculate the severity of an anomaly.

        Args:
            value: Detected value
            expected: Expected value
            threshold: Threshold for anomaly detection

        Returns:
            Severity score between 0.0 and 1.0
        """
        if expected == 0:
            # Avoid division by zero
            if value == 0:
                return 0.0
            return 1.0

        # Calculate relative deviation
        deviation = abs(value - expected) / abs(expected)

        # Normalize to 0-1 range using the threshold
        severity = deviation / threshold

        # Cap at 1.0
        return min(severity, 1.0)


# ------------------------------------------------------------------------------
# Utility Functions
# ------------------------------------------------------------------------------

_anomaly_detector_instance = None


def set_anomaly_detector(detector: AnomalyDetector) -> None:
    """
    Set the global anomaly detector instance.

    This allows replacing the default detector with a custom implementation.

    Args:
        detector: Anomaly detector instance to use globally
    """
    global _anomaly_detector_instance
    _anomaly_detector_instance = detector


# Example: Publish anomaly event
# event_bus.publish("anomaly_detected", anomaly_data)
