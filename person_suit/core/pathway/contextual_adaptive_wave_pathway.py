"""
Contextual Adaptive Wave (CAW) Pathway System
=============================================

This module implements the Contextual Adaptive Wave (CAW) pathway system for 
communication between different pathways in the Person Suit system.

The CAW pathway system embodies core CAW principles:
- Dual Wave-Particle Information: Messages have both wave and particle aspects
- Contextual Computation: All processing is context-aware and adaptive
- Adaptive Computational Fidelity: Resource usage adapts to context
- Concurrent Reactive Entities: Pathways as CAW actors

Related Files:
- person_suit/core/context/core.py: Context registry
- person_suit/core/effects/base.py: Effect types
- person_suit/meta_systems/persona_core/interfaces/pathway_interfaces.py: Pathway interfaces

Dependencies:
- Python 3.8+
- asyncio
"""

import asyncio
import logging
import math
import time
import uuid
from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Any, Dict, List, Optional, Union, TypeVar, Generic, Callable

from person_suit.core.context.unified import UnifiedContext
from person_suit.core.effects.base import Effect, EffectType
from person_suit.core.infrastructure.wave.core import WaveFunction

logger = logging.getLogger(__name__)

# Type variables for generic pathway handling
R = TypeVar('R')  # Return type
M = TypeVar('M')  # Message type


class PathwayType(Enum):
    """Types of pathways in the CAW system."""
    CAM = "cam"  # Computational-Algorithmic-Mathematical
    SEM = "sem"  # Subjective-Experiential-Metaphorical  
    SOMA = "soma"  # Somatic/State


@dataclass
class PathwayMessage:
    """
    Base message class for CAW pathway communication.
    
    Embodies Dual Wave-Particle Information principle by containing
    both discrete message data (particle) and wave function (wave).
    """
    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    pathway_type: PathwayType = PathwayType.CAM
    data: Dict[str, Any] = field(default_factory=dict)
    wave_function: Optional[WaveFunction] = None
    timestamp: float = field(default_factory=time.time)
    
    def to_wave_representation(self, context: UnifiedContext) -> WaveFunction:
        """Convert message to wave representation."""
        if self.wave_function:
            return self.wave_function
            
        # Create default wave function based on message content
        def amplitude_func(content, ctx):
            # Higher amplitude for more important messages
            importance = self.data.get('importance', 0.5)
            context_alignment = 1.0 if ctx.domain == self.pathway_type.value else 0.5
            return importance * context_alignment
            
        def phase_func(content, ctx):
            # Phase based on message urgency and context priority
            urgency = self.data.get('urgency', 0.5)
            return urgency * math.pi
            
        return WaveFunction(amplitude_func, phase_func)
    
    def to_particle_representation(self) -> Dict[str, Any]:
        """Convert message to discrete particle representation."""
        return {
            'message_id': self.message_id,
            'pathway_type': self.pathway_type.value,
            'data': self.data,
            'timestamp': self.timestamp
        }


@dataclass
class ContextualPathwayMessage(PathwayMessage):
    """
    Context-aware pathway message implementing Contextual Computation principle.
    
    All processing is modulated by context, ensuring relevance and adaptability.
    """
    context: Optional[UnifiedContext] = None
    context_domain: Optional[str] = None
    adaptive_fidelity: float = 1.0  # ACF setting
    
    def set_context(self, context: UnifiedContext) -> None:
        """Set the processing context for this message."""
        self.context = context
        self.context_domain = context.domain
        
        # Adapt fidelity based on context (ACF principle)
        if hasattr(context, 'computational_fidelity'):
            self.adaptive_fidelity = context.computational_fidelity
    
    def adapt_to_context(self, context: UnifiedContext) -> 'ContextualPathwayMessage':
        """Create an adapted version of this message for the given context."""
        adapted = ContextualPathwayMessage(
            message_id=self.message_id,
            pathway_type=self.pathway_type,
            data=self.data.copy(),
            wave_function=self.wave_function,
            timestamp=self.timestamp,
            context=context,
            context_domain=context.domain
        )
        
        # Adapt data based on context
        if context.domain == 'high_performance':
            adapted.data['processing_mode'] = 'optimized'
        elif context.domain == 'low_resource':
            adapted.data['processing_mode'] = 'minimal'
            
        return adapted


class ContextualPathwayHandler(Generic[M, R]):
    """
    Base handler for contextual pathway processing.
    
    Implements Concurrent Reactive Entities (CAW Actors) principle
    by providing reactive, context-aware message processing.
    """
    
    def __init__(self, pathway_type: PathwayType):
        """Initialize the pathway handler."""
        self.pathway_type = pathway_type
        self.logger = logging.getLogger(f"{__name__}.{pathway_type.value}")
    
    async def handle_message(self, message: M, context: UnifiedContext) -> Optional[R]:
        """
        Handle a message with full context awareness.
        
        Args:
            message: The message to process
            context: The unified context for processing
            
        Returns:
            Optional processing result
        """
        # Default implementation - subclasses should override
        self.logger.info(f"Processing {type(message).__name__} in {self.pathway_type.value} pathway")
        return None
    
    def create_wave_function(self, message: M, context: UnifiedContext) -> WaveFunction:
        """Create a wave function for the message in the given context."""
        def amplitude_func(content, ctx):
            # Default amplitude based on message type and context alignment
            base_amplitude = 0.7
            context_boost = 0.3 if ctx.domain == self.pathway_type.value else 0.0
            return base_amplitude + context_boost
            
        def phase_func(content, ctx):
            # Default phase based on processing urgency
            urgency = getattr(message, 'urgency', 0.5) if hasattr(message, 'urgency') else 0.5
            return urgency * math.pi / 2
            
        return WaveFunction(amplitude_func, phase_func)


class PathwaySystem:
    """
    Core CAW pathway system implementing all CAW principles.
    
    This system coordinates multiple pathway handlers and provides
    context-aware routing and processing capabilities.
    """
    
    def __init__(self):
        """Initialize the pathway system."""
        self.handlers: Dict[PathwayType, ContextualPathwayHandler] = {}
        self.logger = logging.getLogger(__name__)
        
    def register_handler(self, pathway_type: PathwayType, handler: ContextualPathwayHandler) -> None:
        """Register a pathway handler."""
        self.handlers[pathway_type] = handler
        self.logger.info(f"Registered handler for {pathway_type.value} pathway")
    
    async def route_message(self, message: ContextualPathwayMessage) -> Optional[Any]:
        """
        Route a message to the appropriate pathway handler.
        
        Implements Contextual Computation by adapting routing based on context.
        """
        handler = self.handlers.get(message.pathway_type)
        if not handler:
            self.logger.warning(f"No handler registered for {message.pathway_type.value}")
            return None
            
        # Ensure message has context
        if not message.context:
            # Create default context if none provided
            message.set_context(UnifiedContext.create_default(message.pathway_type.value))
            
        return await handler.handle_message(message, message.context)
    
    def create_interference_pattern(self, messages: List[ContextualPathwayMessage]) -> WaveFunction:
        """
        Create interference patterns from multiple messages.
        
        Implements Physics-Inspired Dynamics through wave interference.
        """
        def combined_amplitude(content, context):
            total = 0.0
            for msg in messages:
                wave = msg.to_wave_representation(context)
                total += wave.amplitude(content, context)
            return total / len(messages) if messages else 0.0
            
        def combined_phase(content, context):
            total = 0.0
            for msg in messages:
                wave = msg.to_wave_representation(context)
                total += wave.phase(content, context)
            return total / len(messages) if messages else 0.0
            
        return WaveFunction(combined_amplitude, combined_phase)


# Global pathway system instance
_pathway_system: Optional[PathwaySystem] = None


def get_pathway_system() -> PathwaySystem:
    """Get the global pathway system instance."""
    global _pathway_system
    if _pathway_system is None:
        _pathway_system = PathwaySystem()
    return _pathway_system


__all__ = [
    'PathwayType',
    'PathwayMessage', 
    'ContextualPathwayMessage',
    'ContextualPathwayHandler',
    'PathwaySystem',
    'get_pathway_system'
]
