"""
Contextual Adaptive Wave (CAW) Pathway System
=============================================

This module implements the Contextual Adaptive Wave (CAW) pathway system for
communication between different pathways in the Person Suit system.

The CAW pathway system embodies core CAW principles:
- Dual Wave-Particle Information: Messages have both wave and particle aspects
- Contextual Computation: All processing is context-aware and adaptive
- Adaptive Computational Fidelity: Resource usage adapts to context
- Concurrent Reactive Entities: Pathways as CAW actors

Related Files:
- person_suit/core/context/core.py: Context registry
- person_suit/core/effects/base.py: Effect types
- person_suit/meta_systems/persona_core/interfaces/pathway_interfaces.py: Pathway interfaces

Dependencies:
- Python 3.8+
- asyncio
"""

import asyncio
import logging
import math
import time
import uuid
from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Any, Dict, Generic, List, Optional, TypeVar, Union

from person_suit.core.constants.fixed_point_scale import PRIO_NORMAL, SCALE
from person_suit.core.context.unified import UnifiedContext as Context
from person_suit.core.context.core import ContextRegistry
from person_suit.core.effects.base import EffectType
# We'll use the actual EffectType values from the enum

# Import proper Effect types (no longer using the anti-pattern)
from person_suit.core.effects.pathway_effects import (
    PathwayRoutingEffect,
    PathwayRegistrationEffect,
    PathwayContextUpdateEffect,
)
from person_suit.core.effects.base import Effect, CompositeEffect


# AsyncSingleton class implementation (since patterns.py was deleted)
class AsyncSingleton:
    """Async singleton metaclass implementation."""

    _instances = {}
    _locks = {}

    @classmethod
    async def get_instance(cls):
        if cls not in cls._instances:
            if cls not in cls._locks:
                cls._locks[cls] = asyncio.Lock()
            async with cls._locks[cls]:
                if cls not in cls._instances:
                    instance = cls()
                    if hasattr(instance, "initialize"):
                        await instance.initialize()
                    cls._instances[cls] = instance
        return cls._instances[cls]


# telemetry decorator (placeholder since telemetry.py imports were deleted)
def telemetry(func):
    """Telemetry decorator placeholder."""
    return func


# Define missing event types that were imported from non-existent modules
class ComponentEventType(Enum):
    """Types of component events for pathway communication."""

    CAM_TO_SEM_DATA = auto()
    SEM_TO_CAM_DATA = auto()
    CAM_TO_SOMA_DATA = auto()
    SOMA_TO_CAM_DATA = auto()
    SEM_TO_SOMA_DATA = auto()
    SOMA_TO_SEM_DATA = auto()
    CUSTOM_COMPONENT_EVENT = auto()


# Placeholder classes for missing CAW concepts
class Information:
    """Placeholder for Information class"""

    def __init__(self, data: Any, wave_function: Any):
        self.data = data
        self.wave_function = wave_function

    def interpret(self, context: Context) -> Any:
        # Placeholder implementation
        return type("Interpretation", (), {"probability": 0.5})()


class WaveFunction:
    """Placeholder for WaveFunction class"""

    def __init__(self, amplitude_fn: Any, phase_fn: Any):
        self.amplitude_fn = amplitude_fn
        self.phase_fn = phase_fn


# CAWProcessor placeholder - needs proper implementation
class CAWProcessor:
    """Placeholder for CAWProcessor - needs proper implementation"""

    def __init__(self):
        self.wave_functions = {}

    def register_wave_function(self, name: str, wave_function: WaveFunction) -> None:
        self.wave_functions[name] = wave_function


# Configure logging
logger = logging.getLogger(__name__)

# Type variables
T = TypeVar("T")
R = TypeVar("R")


class PathwayType(Enum):
    """Types of pathways in the Person Suit system."""

    CAM = "computational_analytical"  # Computational-Analytical Module
    SEM = "subjective_experiential"  # Subjective-Experiential Module
    SOMA = "self_organizing_memory"  # Self-Organizing Memory Architecture
    CUSTOM = "custom"  # Custom pathway type


@dataclass
class PathwayMessage:
    """
    A message sent between pathways.

    Attributes:
        content: The content of the message
        source_pathway: The source pathway type
        target_pathway: The target pathway type
        message_id: Unique identifier for the message
        correlation_id: Optional correlation ID for related messages
        timestamp: The time when the message was created
        metadata: Optional metadata for the message
    """

    content: Any
    source_pathway: PathwayType
    target_pathway: PathwayType
    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    correlation_id: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ContextualPathwayMessage(PathwayMessage):
    """
    A context-aware message sent between pathways.

    Extends the standard PathwayMessage with context-related attributes.

    Attributes:
        context_domain: The domain of the context in which the message should be processed
        context_priority: The priority of the context in which the message should be processed
        context_constraints: Constraints of the context in which the message should be processed
    """

    context_domain: Optional[str] = None
    context_priority: Optional[int] = None  # Changed to int for proper priority handling
    context_constraints: Optional[List[str]] = None

    def get_context(self) -> Context:
        """
        Get the context for this message.

        Returns:
            A context object based on the message's context attributes
        """
        return Context(
            domain=self.context_domain or "pathway_standard",
            priority=self.context_priority or PRIO_NORMAL,
            metadata={"constraints": self.context_constraints or []},
        )

    def set_context(self, context: Context) -> None:
        """
        Set the context attributes based on a context object.

        Args:
            context: The context to use
        """
        self.context_domain = context.domain
        self.context_priority = context.priority
        # Extract constraints from metadata if available
        if "constraints" in context.metadata:
            self.context_constraints = context.metadata["constraints"]


class PathwayHandler(Generic[T, R]):
    """
    Base class for pathway message handlers.

    Attributes:
        pathway_type: The type of pathway this handler belongs to
    """

    def __init__(self, pathway_type: PathwayType):
        """
        Initialize the pathway handler.

        Args:
            pathway_type: The type of pathway this handler belongs to
        """
        self.pathway_type = pathway_type

    async def handle_message(self, message: PathwayMessage) -> Optional[R]:
        """
        Handle a message.

        Args:
            message: The message to handle

        Returns:
            Optional response
        """
        raise NotImplementedError("Subclasses must implement handle_message")


class ContextualPathwayHandler(PathwayHandler[T, R]):
    """
    Context-aware pathway message handler.

    Extends the standard PathwayHandler with context-aware capabilities.

    Attributes:
        pathway_type: The type of pathway this handler belongs to
        context_registry: Registry of available contexts
        caw_processor: Processor for context-aware wave processing
    """

    def __init__(
        self,
        pathway_type: PathwayType,
        context_registry: Optional[ContextRegistry] = None,
        caw_processor: Optional[CAWProcessor] = None,
    ):
        """
        Initialize the context-aware pathway handler.

        Args:
            pathway_type: The type of pathway this handler belongs to
            context_registry: Optional registry of contexts
            caw_processor: Optional CAW processor
        """
        super().__init__(pathway_type)
        self.context_registry = context_registry or ContextRegistry()
        self.caw_processor = caw_processor or CAWProcessor()

        # Register standard contexts
        self._register_standard_contexts()

        # Register wave functions
        self._register_wave_functions()

    def _register_standard_contexts(self) -> None:
        """Register standard contexts with the context registry."""
        # Standard pathway contexts
        standard_context = Context(
            domain="pathway_standard",
            priority=PRIO_NORMAL,
            metadata={"constraints": ["default", "general"]},
        )

        high_priority_context = Context(
            domain="pathway_high_priority",
            priority=int(0.8 * SCALE),  # High priority using integer scale
            metadata={"constraints": ["important", "time-sensitive"]},
        )

        low_priority_context = Context(
            domain="pathway_low_priority",
            priority=int(0.2 * SCALE),  # Low priority using integer scale
            metadata={"constraints": ["deferrable", "non-critical"]},
        )

        # Pathway-specific contexts
        cam_context = Context(
            domain="pathway_cam",
            priority=int(0.6 * SCALE),  # Analytical priority
            metadata={"constraints": ["logical", "computational", "analytical"]},
        )

        sem_context = Context(
            domain="pathway_sem",
            priority=int(0.5 * SCALE),  # Experiential priority
            metadata={"constraints": ["emotional", "subjective", "experiential"]},
        )

        soma_context = Context(
            domain="pathway_soma",
            priority=int(0.5 * SCALE),  # Memory priority
            metadata={"constraints": ["storage", "retrieval", "organization"]},
        )

        # Register contexts
        self.context_registry.register_context("standard", standard_context)
        self.context_registry.register_context("high_priority", high_priority_context)
        self.context_registry.register_context("low_priority", low_priority_context)
        self.context_registry.register_context("cam", cam_context)
        self.context_registry.register_context("sem", sem_context)
        self.context_registry.register_context("soma", soma_context)

        # Set default context
        self.context_registry.set_default_context(standard_context)

    def _register_wave_functions(self) -> None:
        """Register wave functions for pathway messages."""
        # Wave function for pathway messages
        message_wave = WaveFunction(
            # Amplitude function based on message importance and context
            lambda msg, ctx: self._calculate_message_amplitude(msg, ctx),
            # Phase function based on message-context alignment
            lambda msg, ctx: self._calculate_message_phase(msg, ctx),
        )

        # Register with CAW processor
        self.caw_processor.register_wave_function("pathway_message", message_wave)

    def _calculate_message_amplitude(
        self, message: ContextualPathwayMessage, context: Context
    ) -> float:
        """
        Calculate the amplitude of a message in a context.

        Args:
            message: The message
            context: The context

        Returns:
            The amplitude (0.0 to 1.0)
        """
        # Start with base amplitude
        amplitude = 0.5

        # Check for context domain alignment
        if message.context_domain == context.domain:
            amplitude = 0.8

        # Check for context priority alignment
        if message.context_priority == context.priority:
            amplitude = max(amplitude, 0.7)

        # Check for context constraints alignment
        constraints = context.metadata.get("constraints", [])
        if message.context_constraints and constraints:
            matching_constraints = [c for c in message.context_constraints if c in constraints]
            if matching_constraints:
                constraint_factor = len(matching_constraints) / len(message.context_constraints)
                amplitude = max(amplitude, 0.6 + 0.4 * constraint_factor)

        # Check for pathway-specific alignment
        if message.source_pathway == PathwayType.CAM and context.domain == "pathway_cam":
            amplitude = max(amplitude, 0.9)
        elif message.source_pathway == PathwayType.SEM and context.domain == "pathway_sem":
            amplitude = max(amplitude, 0.9)
        elif message.source_pathway == PathwayType.SOMA and context.domain == "pathway_soma":
            amplitude = max(amplitude, 0.9)

        return amplitude

    def _calculate_message_phase(
        self, message: ContextualPathwayMessage, context: Context
    ) -> float:
        """
        Calculate the phase of a message in a context.

        Args:
            message: The message
            context: The context

        Returns:
            The phase (0.0 to 2π)
        """
        # Default phase (90 degrees)
        phase = math.pi / 2

        # Check for context domain alignment
        if message.context_domain == context.domain:
            phase = 0.0  # Perfect alignment (0 degrees)
        elif message.context_domain and context.domain:
            # Partial alignment based on domain similarity
            if message.context_domain.startswith(context.domain) or context.domain.startswith(
                message.context_domain
            ):
                phase = math.pi / 4  # Partial alignment (45 degrees)

        return phase

    def _determine_best_context(self, message: ContextualPathwayMessage) -> Context:
        """
        Determine the best context for a message.

        Args:
            message: The message

        Returns:
            The best context for the message
        """
        # Get all available contexts
        contexts = list(self.context_registry.get_all_contexts().values())
        if not contexts:
            # Use default context if no contexts are available
            return self.context_registry.get_default_context() or Context(
                domain="pathway_standard",
                priority=PRIO_NORMAL,
                metadata={"constraints": ["default", "general"]},
            )

        # Get the wave function for pathway messages
        wave_function = self.caw_processor.wave_functions.get("pathway_message")
        if wave_function is None:
            # Use default context if no wave function is available
            return self.context_registry.get_default_context() or contexts[0]

        # Create information with the wave function
        information = Information(message, wave_function)

        # Interpret the message in each context
        interpretations = [information.interpret(context) for context in contexts]

        # Select the context with the highest probability
        best_index = max(range(len(interpretations)), key=lambda i: interpretations[i].probability)
        return contexts[best_index]

    async def handle_message(
        self, message: Union[PathwayMessage, ContextualPathwayMessage]
    ) -> Optional[R]:
        """
        Handle a message with context awareness.

        Args:
            message: The message to handle

        Returns:
            Optional response
        """
        # Convert standard message to contextual message if needed
        contextual_message = message
        if not isinstance(message, ContextualPathwayMessage):
            contextual_message = ContextualPathwayMessage(
                content=message.content,
                source_pathway=message.source_pathway,
                target_pathway=message.target_pathway,
                message_id=message.message_id,
                correlation_id=message.correlation_id,
                timestamp=message.timestamp,
                metadata=message.metadata.copy(),
            )

        # Determine the best context if none is set
        if not contextual_message.context_domain:
            context = self._determine_best_context(contextual_message)
            contextual_message.set_context(context)

        # Process the message in the determined context
        return await self.process_message_in_context(
            contextual_message, contextual_message.get_context()
        )

    async def process_message_in_context(
        self, message: ContextualPathwayMessage, context: Context
    ) -> Optional[R]:
        """
        Process a message in a specific context.

        Args:
            message: The message to process
            context: The context in which to process the message

        Returns:
            Optional response
        """
        raise NotImplementedError("Subclasses must implement process_message_in_context")


class CAWPathwaySystem(AsyncSingleton):
    """
    Context-aware pathway system for communication between pathways.

    This class implements a context-aware pathway system using CAW principles,
    enabling context-sensitive communication between different pathways in the
    Person Suit system.
    """

    async def initialize(self, config: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the context-aware pathway system.

        Args:
            config: Optional configuration for the pathway system
        """
        self.config = config or {}
        self.logger = logger

        # Initialize handlers
        self._handlers: Dict[PathwayType, List[ContextualPathwayHandler]] = {
            PathwayType.CAM: [],
            PathwayType.SEM: [],
            PathwayType.SOMA: [],
            PathwayType.CUSTOM: [],
        }

        # Initialize context registry
        self._context_registry = ContextRegistry()

        # Initialize CAW processor
        self._caw_processor = CAWProcessor()

        # Initialize HybridMessageBus (replacing deprecated EventBus)
        from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus

        self._bus = get_message_bus()
        if not self._bus.is_running():
            await self._bus.start()

        # Initialize locks
        self._handlers_lock = asyncio.Lock()

        # Register standard contexts
        self._register_standard_contexts()

        # Register wave functions
        self._register_wave_functions()

        # Flag for initialization
        self.is_initialized = True
        self.logger.info("Context-aware pathway system initialized")

    def _register_standard_contexts(self) -> None:
        """Register standard contexts with the context registry."""
        # Standard pathway contexts
        standard_context = Context(
            domain="pathway_standard",
            priority=PRIO_NORMAL,
            metadata={"constraints": ["default", "general"]},
        )

        high_priority_context = Context(
            domain="pathway_high_priority",
            priority=int(0.8 * SCALE),  # High priority using integer scale
            metadata={"constraints": ["important", "time-sensitive"]},
        )

        low_priority_context = Context(
            domain="pathway_low_priority",
            priority=int(0.2 * SCALE),  # Low priority using integer scale
            metadata={"constraints": ["deferrable", "non-critical"]},
        )

        # Pathway-specific contexts
        cam_context = Context(
            domain="pathway_cam",
            priority=int(0.6 * SCALE),  # Analytical priority
            metadata={"constraints": ["logical", "computational", "analytical"]},
        )

        sem_context = Context(
            domain="pathway_sem",
            priority=int(0.5 * SCALE),  # Experiential priority
            metadata={"constraints": ["emotional", "subjective", "experiential"]},
        )

        soma_context = Context(
            domain="pathway_soma",
            priority=int(0.5 * SCALE),  # Memory priority
            metadata={"constraints": ["storage", "retrieval", "organization"]},
        )

        # Register contexts
        self._context_registry.register_context("standard", standard_context)
        self._context_registry.register_context("high_priority", high_priority_context)
        self._context_registry.register_context("low_priority", low_priority_context)
        self._context_registry.register_context("cam", cam_context)
        self._context_registry.register_context("sem", sem_context)
        self._context_registry.register_context("soma", soma_context)

        # Set default context
        self._context_registry.set_default_context(standard_context)

    def _register_wave_functions(self) -> None:
        """Register wave functions for pathway messages."""
        # Wave function for pathway messages
        message_wave = WaveFunction(
            # Amplitude function based on message importance and context
            lambda msg, ctx: self._calculate_message_amplitude(msg, ctx),
            # Phase function based on message-context alignment
            lambda msg, ctx: self._calculate_message_phase(msg, ctx),
        )

        # Register with CAW processor
        self._caw_processor.register_wave_function("pathway_message", message_wave)

    def _calculate_message_amplitude(
        self, message: ContextualPathwayMessage, context: Context
    ) -> float:
        """
        Calculate the amplitude of a message in a context.

        Args:
            message: The message
            context: The context

        Returns:
            The amplitude (0.0 to 1.0)
        """
        # Start with base amplitude
        amplitude = 0.5

        # Check for context domain alignment
        if message.context_domain == context.domain:
            amplitude = 0.8

        # Check for context priority alignment
        if message.context_priority == context.priority:
            amplitude = max(amplitude, 0.7)

        # Check for context constraints alignment
        constraints = context.metadata.get("constraints", [])
        if message.context_constraints and constraints:
            matching_constraints = [c for c in message.context_constraints if c in constraints]
            if matching_constraints:
                constraint_factor = len(matching_constraints) / len(message.context_constraints)
                amplitude = max(amplitude, 0.6 + 0.4 * constraint_factor)

        # Check for pathway-specific alignment
        if message.source_pathway == PathwayType.CAM and context.domain == "pathway_cam":
            amplitude = max(amplitude, 0.9)
        elif message.source_pathway == PathwayType.SEM and context.domain == "pathway_sem":
            amplitude = max(amplitude, 0.9)
        elif message.source_pathway == PathwayType.SOMA and context.domain == "pathway_soma":
            amplitude = max(amplitude, 0.9)

        return amplitude

    def _calculate_message_phase(
        self, message: ContextualPathwayMessage, context: Context
    ) -> float:
        """
        Calculate the phase of a message in a context.

        Args:
            message: The message
            context: The context

        Returns:
            The phase (0.0 to 2π)
        """
        # Default phase (90 degrees)
        phase = math.pi / 2

        # Check for context domain alignment
        if message.context_domain == context.domain:
            phase = 0.0  # Perfect alignment (0 degrees)
        elif message.context_domain and context.domain:
            # Partial alignment based on domain similarity
            if message.context_domain.startswith(context.domain) or context.domain.startswith(
                message.context_domain
            ):
                phase = math.pi / 4  # Partial alignment (45 degrees)

        return phase

    def _determine_best_context(self, message: ContextualPathwayMessage) -> Context:
        """
        Determine the best context for a message.

        Args:
            message: The message

        Returns:
            The best context for the message
        """
        # Get all available contexts
        contexts = list(self._context_registry.get_all_contexts().values())
        if not contexts:
            # Use default context if no contexts are available
            return self._context_registry.get_default_context() or Context(
                domain="pathway_standard",
                priority=PRIO_NORMAL,
                metadata={"constraints": ["default", "general"]},
            )

        # Get the wave function for pathway messages
        wave_function = self._caw_processor.wave_functions.get("pathway_message")
        if wave_function is None:
            # Use default context if no wave function is available
            return self._context_registry.get_default_context() or contexts[0]

        # Create information with the wave function
        information = Information(message, wave_function)

        # Interpret the message in each context
        interpretations = [information.interpret(context) for context in contexts]

        # Select the context with the highest probability
        best_index = max(range(len(interpretations)), key=lambda i: interpretations[i].probability)
        return contexts[best_index]

    async def register_handler(self, handler: ContextualPathwayHandler) -> Effect:
        """
        Register a handler for a pathway.

        Args:
            handler: The handler to register

        Returns:
            Effect describing the registration intent
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        # Return an Effect instead of performing the action
        return PathwayRegistrationEffect(
            pathway_type=handler.pathway_type.value,
            handler_id=str(id(handler)),
            handler_metadata={
                "class": handler.__class__.__name__,
                "pathway": handler.pathway_type.value,
            },
        )

    @effects([EffectType.STATE_WRITE])
    async def unregister_handler(self, handler: ContextualPathwayHandler) -> Effect:
        """
        Unregister a handler for a pathway.

        Args:
            handler: The handler to unregister

        Returns:
            Effect describing the unregistration intent
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        # Return an Effect for unregistration (using registration with 'remove' operation)
        effect = PathwayRegistrationEffect(
            pathway_type=handler.pathway_type.value,
            handler_id=str(id(handler)),
            handler_metadata={
                "class": handler.__class__.__name__,
                "pathway": handler.pathway_type.value,
                "operation": "remove",
            },
        )
        effect.operation = "unregister_handler"
        return effect

    @effects([EffectType.COMMUNICATION])
    @telemetry
    async def send_message(
        self,
        message: Union[PathwayMessage, ContextualPathwayMessage],
        context: Optional[Context] = None,
    ) -> Effect:
        """
        Send a message to a pathway.

        Args:
            message: The message to send
            context: Optional context for the message

        Returns:
            Effect describing the message routing intent
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        # Convert standard message to contextual message if needed
        contextual_message = message
        if not isinstance(message, ContextualPathwayMessage):
            contextual_message = ContextualPathwayMessage(
                content=message.content,
                source_pathway=message.source_pathway,
                target_pathway=message.target_pathway,
                message_id=message.message_id,
                correlation_id=message.correlation_id,
                timestamp=message.timestamp,
                metadata=message.metadata.copy(),
            )

        # Set context if provided
        if context is not None:
            contextual_message.set_context(context)
        elif not contextual_message.context_domain:
            # Determine best context if none is set
            best_context = self._determine_best_context(contextual_message)
            contextual_message.set_context(best_context)

        # Return an Effect instead of performing the routing
        return PathwayRoutingEffect(
            source_pathway=contextual_message.source_pathway.value,
            target_pathway=contextual_message.target_pathway.value,
            message_id=contextual_message.message_id,
            message_content=contextual_message.content,
            correlation_id=contextual_message.correlation_id,
        )

    @effects([EffectType.STATE_WRITE])
    async def register_context(self, name: str, context: Context) -> Effect:
        """
        Register a context with the pathway system.

        Args:
            name: The name of the context
            context: The context to register

        Returns:
            Effect describing the context registration intent
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        # Extract constraints from metadata
        constraints = context.metadata.get("constraints", [])

        return PathwayContextUpdateEffect(
            context_name=name,
            context_domain=context.domain,
            context_priority=context.priority,
            context_constraints=constraints,
        )

    @effects([EffectType.STATE_READ])
    async def get_context(self, name: str) -> Optional[Context]:
        """
        Get a context by name.

        Args:
            name: The name of the context

        Returns:
            Optional[Context]: The context, or None if not found
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        return self._context_registry.get_context(name)

    @effects([EffectType.STATE_READ])
    async def get_all_contexts(self) -> Dict[str, Context]:
        """
        Get all registered contexts.

        Returns:
            Dict[str, Context]: All registered contexts
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        return self._context_registry.get_all_contexts()

    @effects([EffectType.STATE_WRITE])
    async def set_default_context(self, context: Context) -> Effect:
        """
        Set the default context.

        Args:
            context: The context to set as default

        Returns:
            Effect describing the default context update intent
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        # Extract constraints from metadata
        constraints = context.metadata.get("constraints", [])

        effect = PathwayContextUpdateEffect(
            context_name="default",
            context_domain=context.domain,
            context_priority=context.priority,
            context_constraints=constraints,
        )
        effect.operation = "set_default"
        return effect


# Convenience function to get the CAW pathway system
async def get_caw_pathway_system() -> CAWPathwaySystem:
    """
    Get the singleton instance of the CAW pathway system.

    Returns:
        CAWPathwaySystem: The CAW pathway system instance
    """
    return await CAWPathwaySystem.get_instance()
