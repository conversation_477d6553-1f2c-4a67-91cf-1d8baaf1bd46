"""Capability-based authentication implementation for Person Suit.

File: person_suit/core/security/authentication.py
Purpose: Implements proper capability-based authentication following Universal Architectural Principles
Related Files:
- person_suit/core/security/capabilities.py: Capability verification
- person_suit/core/context/unified.py: UnifiedContext with capabilities
- person_suit/core/effects/base.py: Effect system integration
Dependencies: typing, logging, dataclasses
"""

from __future__ import annotations

import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union

from .capabilities import CapabilityToken, Permission, CapabilityScope, verify_capability

logger = logging.getLogger(__name__)


@dataclass
class AuthenticationResult:
    """Result of an authentication attempt."""

    success: bool
    token: Optional[CapabilityToken] = None
    error_message: Optional[str] = None
    granted_capabilities: List[str] = None

    def __post_init__(self):
        if self.granted_capabilities is None:
            self.granted_capabilities = []


@dataclass
class AuthenticationRequest:
    """Request for authentication with capability requirements."""

    subject_id: str
    required_permissions: List[Permission]
    required_scope: CapabilityScope
    context: Optional[Dict[str, Any]] = None
    additional_metadata: Optional[Dict[str, Any]] = None


class AuthenticationManager:
    """
    Capability-based authentication manager following Universal Architectural Principles.

    This implementation enforces Principle III (Capability as Sole Authority) by requiring
    valid capability tokens for all authentication operations. No role-based or stub
    authentication is permitted.

    Implementation:
        - All authentication requires valid capability tokens
        - Context-aware authentication using UnifiedContext
        - Integration with Effect system for declarative operations
        - Proper error handling and logging
    """

    def __init__(self):
        """Initialize the authentication manager."""
        self._token_cache: Dict[str, CapabilityToken] = {}
        logger.info("AuthenticationManager initialized with capability-based security")

    async def authenticate(
        self,
        token: Optional[CapabilityToken] = None,
        required_permission: Optional[Permission] = None,
        required_scope: Optional[CapabilityScope] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> AuthenticationResult:
        """
        Authenticate using capability-based security.

        Args:
            token: Capability token to verify
            required_permission: Required permission for the operation
            required_scope: Required scope for the operation
            context: Additional context for authentication

        Returns:
            AuthenticationResult with success status and details

        Raises:
            ValueError: If required parameters are missing
        """
        if token is None:
            logger.warning("Authentication failed: No capability token provided")
            return AuthenticationResult(
                success=False,
                error_message="Capability token is required for authentication"
            )

        if required_permission is None:
            logger.warning("Authentication failed: No required permission specified")
            return AuthenticationResult(
                success=False,
                error_message="Required permission must be specified"
            )

        if required_scope is None:
            logger.warning("Authentication failed: No required scope specified")
            return AuthenticationResult(
                success=False,
                error_message="Required scope must be specified"
            )

        try:
            # Verify the capability token
            is_authorized = verify_capability(token, required_permission, required_scope)

            if is_authorized:
                logger.info(f"Authentication successful for permission {required_permission.value}")
                return AuthenticationResult(
                    success=True,
                    token=token,
                    granted_capabilities=getattr(token, "permissions", [])
                )
            else:
                logger.warning(f"Authentication failed: Insufficient capabilities for {required_permission.value}")
                return AuthenticationResult(
                    success=False,
                    error_message=f"Insufficient capabilities for {required_permission.value}"
                )

        except Exception as e:
            logger.error(f"Authentication error: {e}", exc_info=True)
            return AuthenticationResult(
                success=False,
                error_message=f"Authentication error: {str(e)}"
            )

    async def authenticate_request(self, request: AuthenticationRequest) -> AuthenticationResult:
        """
        Authenticate a structured authentication request.

        Args:
            request: AuthenticationRequest with all required parameters

        Returns:
            AuthenticationResult with success status and details
        """
        # For now, we'll require the first permission in the list
        # In a full implementation, this would check all required permissions
        primary_permission = request.required_permissions[0] if request.required_permissions else None

        if primary_permission is None:
            return AuthenticationResult(
                success=False,
                error_message="No permissions specified in authentication request"
            )

        # This is a simplified implementation - in practice, we'd need to get the token
        # from the request context or a token store
        logger.warning("authenticate_request called but no token provided in request")
        return AuthenticationResult(
            success=False,
            error_message="Token must be provided for authentication"
        )

    def cache_token(self, token_id: str, token: CapabilityToken) -> None:
        """Cache a capability token for future use."""
        self._token_cache[token_id] = token
        logger.debug(f"Cached capability token {token_id}")

    def get_cached_token(self, token_id: str) -> Optional[CapabilityToken]:
        """Retrieve a cached capability token."""
        return self._token_cache.get(token_id)

    def clear_token_cache(self) -> None:
        """Clear the token cache."""
        self._token_cache.clear()
        logger.info("Token cache cleared")