"""person_suit.core.security.capabilities

Temporary façade for capability-based checks used by core actor system prior to
full Sprint-4 security implementation.

It imports canonical structures where they already exist and defines minimal
fallback stubs otherwise so modules importing
``person_suit.core.security.capabilities`` do not crash during test collection.

TODO (Sprint-4): Replace with full-featured implementation wired to
AdaptiveSecurityManager and capability validator.
"""

from __future__ import annotations

import enum
from typing import Optional

from person_suit.core.security_types import CapabilityScope as _Scope  # Existing rich scope
from person_suit.monitoring.metrics import INVALID_TOKENS_TOTAL  # noqa: WPS433
from person_suit.security.capability import CapabilityToken as _CapTok

# ---------------------------------------------------------------------------
# Re-export canonical classes under expected names
# ---------------------------------------------------------------------------

CapabilityScope = _Scope  # noqa: N816  – keep camelcase for legacy import
CapabilityToken = _CapTok  # noqa: N816

CapabilityScope.ACTOR = staticmethod(lambda resource_id: CapabilityScope("actor", resource_id))  # type: ignore[attr-defined]  # noqa: E501


class Permission(enum.Enum):  # noqa: D101
    CREATE_CHILD = "create_child"
    WATCH_ACTOR = "watch_actor"
    STOP_ACTOR = "stop_actor"
    RESTART_ACTOR = "restart_actor"
    APPLY_EFFECT = "apply_effect"
    READ_STATE = "read_state"
    READ = "read"
    WRITE = "write"
    ANY_ACTION = "any_action"


def verify_capability(
    token: Optional[CapabilityToken],
    permission: Permission,
    scope: CapabilityScope,
) -> bool:  # noqa: D401
    """
    Verify capability token against required permission and scope.

    This implementation enforces Principle III (Capability as Sole Authority) by
    requiring valid capability tokens for all operations. No operations are
    permitted without proper capability tokens.

    Args:
        token: Capability token to verify (REQUIRED - None will be rejected)
        permission: Required permission for the operation
        scope: Required scope for the operation

    Returns:
        bool: True if token grants the required permission, False otherwise

    Security Note:
        This function implements zero-trust security. All operations require
        valid capability tokens. No bypass mechanisms are provided.
    """

    # CRITICAL SECURITY FIX: Reject all operations without valid tokens
    if token is None:
        INVALID_TOKENS_TOTAL.inc()
        return False

    try:
        # Verify token has required permission
        token_permissions = getattr(token, "permissions", [])
        if not token_permissions:
            INVALID_TOKENS_TOTAL.inc()
            return False

        # Check if the specific permission is granted
        allowed = permission.value in token_permissions

        # Additional scope validation could be added here
        # For now, we focus on permission verification

        if not allowed:
            INVALID_TOKENS_TOTAL.inc()

        return allowed

    except Exception:  # pragma: no cover
        # Any exception in capability verification results in denial
        INVALID_TOKENS_TOTAL.inc()
        return False


__all__ = [
    "CapabilityScope",
    "CapabilityToken",
    "Permission",
    "verify_capability",
]
