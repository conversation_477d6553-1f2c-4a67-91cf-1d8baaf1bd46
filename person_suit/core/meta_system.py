"""
meta_system.py

Defines the core meta-system interfaces and minimal stub implementations for the Person Suit architecture.

- PersonaCore: The primary persona logic and state manager.
- Analyst: Analytical/context extraction subsystem.
- Predictor: Prediction and hypothesis generation subsystem.

Related Files:
    - io_layer/shared_io.py: Shared I/O interface for external communication.
    - main.py: Bootstrap and wiring of meta-systems.

Dependencies:
    - Python standard library (abc, typing)
    - Core context module only (no meta_systems dependencies)
"""

from __future__ import annotations

import asyncio
from abc import ABC
from abc import abstractmethod
from typing import Any
from typing import Dict
from typing import Protocol

# Message-based services initialization


async def _ensure_message_services():
    """Ensure message-based services are initialized."""
        raise RuntimeError("Failed to initialize message-based services")


# Core context import (CAW Principle #2) - This is allowed as it's within core
from person_suit.core.context.unified import UnifiedContext

# === Abstract Interfaces for Dependency Inversion ===


class IMemorySystem(Protocol):
    """Protocol for memory system dependency injection."""

    async def store_memory(
        self, input_data: Any, target_layer: Any, context: Dict[str, Any]
    ) -> str:
        """Store memory in the specified layer."""
        ...


class IMemoryLayerType(Protocol):
    """Protocol for memory layer type constants."""

    WORKING: Any
    LONG_TERM: Any
    SENSORY: Any


class IMemoryType(Protocol):
    """Protocol for memory type constants."""

    EPISODIC: Any
    SEMANTIC: Any
    PROCEDURAL: Any


# === Core Meta-System Interfaces ===


class PersonaCore(ABC):
    """Interface for the Persona Core meta-system.

    Responsible for managing persona state, context, and primary logic.
    """

    @abstractmethod
    async def process_event(self, event: Any, context: UnifiedContext) -> None:
        """Process an incoming event in the persona context.

        Args:
            event: The event object to process.
            context: UnifiedContext for contextual integration.
        """
        pass


class Analyst(ABC):
    """Interface for the Analyst meta-system.

    Responsible for context extraction, analysis, and feedback.
    """

    @abstractmethod
    def analyze(self, input_data: Any, context: UnifiedContext) -> Any:
        """Analyze input data and return analysis results.

        Args:
            input_data: Input data to analyze.
            context: UnifiedContext for contextual integration.

        Returns:
            Analysis result (type depends on implementation).
        """
        pass


class Predictor(ABC):
    """Interface for the Predictor meta-system.

    Responsible for generating predictions or hypotheses based on input data.
    """

    @abstractmethod
    def predict(self, context: UnifiedContext) -> Any:
        """Generate a prediction or hypothesis from context.

        Args:
            context: UnifiedContext for contextual integration.

        Returns:
            Prediction or hypothesis (type depends on implementation).
        """
        pass


# === Concrete Implementations (Dependency Injection Ready) ===

# ConcretePersonaCore DELETED - Replaced with TrueMessageBasedPersonaCore
# This violated Phase 2.1 architecture by creating direct object dependencies:
# self._analyst = analyst
# self._predictor = predictor
#
# Migration: Use TrueMessageBasedPersonaCore with pure service discovery instead.


# ConcreteAnalyst DELETED - Replaced with TrueMessageBasedAnalyst
# This was a legacy implementation that supported the direct dependency pattern.
#
# Migration: Use TrueMessageBasedAnalyst with pure service discovery instead.


# ConcretePredictor DELETED - Replaced with TrueMessageBasedPredictor
# This was a legacy implementation that supported the direct dependency pattern.
#
# Migration: Use TrueMessageBasedPredictor with pure service discovery instead.


def register_with_container(container: Any, context: "UnifiedContext") -> None:
    """Register core meta-system components with the DI container.

    Args:
        container: The DI container to register services with.
        context: UnifiedContext for the registration operation (REQUIRED)

    Raises:
        ValueError: If context is None
    """
    if context is None:
        raise ValueError("context is required for DI registration (Principle II)")
    # PHASE 2.1: TRUE Message-Based Meta-System Registration - ALL COMPLETE
    # All meta-systems now use pure service discovery implementations

    # Use CAW-optimal message-based meta-systems; competing "true_message_based_*" stubs were removed.
    from person_suit.core.meta_system_message_based import (
        create_message_based_persona_core,
        create_message_based_analyst,
        create_message_based_predictor,
    )

    # ✅ Register ALL TRUE message-based meta-systems (Phase 2.1 - NO object dependencies)
    def create_persona_core_sync(c):
        """Synchronous factory for PersonaCore - creates async task if needed."""
        try:
            loop = asyncio.get_running_loop()
            # If in async context, create task
            return loop.create_task(
                create_message_based_persona_core(
                    memory_system=c.resolve_optional(IMemorySystem),
                    memory_layer_type=c.resolve_optional(IMemoryLayerType),
                )
            )
        except RuntimeError:
            # No running loop, use asyncio.run
            return asyncio.run(
                create_message_based_persona_core(
                    memory_system=c.resolve_optional(IMemorySystem),
                    memory_layer_type=c.resolve_optional(IMemoryLayerType),
                )
            )

    def create_analyst_sync(c):
        """Synchronous factory for Analyst - creates async task if needed."""
        try:
            loop = asyncio.get_running_loop()
            return loop.create_task(create_message_based_analyst())
        except RuntimeError:
            return asyncio.run(create_message_based_analyst())

    def create_predictor_sync(c):
        """Synchronous factory for Predictor - creates async task if needed."""
        try:
            loop = asyncio.get_running_loop()
            return loop.create_task(create_message_based_predictor())
        except RuntimeError:
            return asyncio.run(create_message_based_predictor())

    container.register(PersonaCore, create_persona_core_sync)
    container.register(Analyst, create_analyst_sync)
    container.register(Predictor, create_predictor_sync)

    # 🎉 Phase 2.1 Achievement: ALL meta-systems use pure service discovery
    # ZERO direct object references between meta-systems!
