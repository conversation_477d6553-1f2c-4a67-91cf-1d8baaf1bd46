"""
File: patterns.py
Purpose: Provides common design patterns for use throughout the codebase.

This module implements various design patterns that are used across the Person Suit
codebase, including Singleton, Factory, Observer, and others. These patterns
help maintain consistency and reduce code duplication.
"""

import asyncio
import inspect
import logging
from typing import Any
from typing import Dict
from typing import Type
from typing import TypeVar
from typing import List
from typing import Callable
import warnings

logger = logging.getLogger(__name__)

T = TypeVar("T")


class AsyncSingleton:
    """
    AsyncSingleton pattern implementation for global service objects.

    This pattern ensures that only one instance of a class exists and provides
    asynchronous initialization, which is important for services that require
    setup operations that are async in nature.

    Example:
        ```python
        class EventBus(AsyncSingleton):
            async def initialize(self):
                self._subscribers = {}

            async def publish(self, event_type, data=None):
                # Implementation

        # Usage
        event_bus = await EventBus.get_instance()
        ```
    """

    _instances: Dict[Type, Any] = {}
    _init_locks: Dict[Type, asyncio.Lock] = {}

    @classmethod
    async def get_instance(cls, *args, **kwargs):
        """
        Get the singleton instance of this class.

        If the instance doesn't exist, it will be created and initialized.

        Args:
            *args: Positional arguments to pass to the constructor
            **kwargs: Keyword arguments to pass to the constructor

        Returns:
            The singleton instance
        """
        # Create instance if it doesn't exist
        if cls not in cls._instances:
            # Use a lock to prevent race conditions
            if cls not in cls._init_locks:
                cls._init_locks[cls] = asyncio.Lock()

            async with cls._init_locks[cls]:
                # Check again in case another task created it while we were waiting
                if cls not in cls._instances:
                    cls._instances[cls] = cls(*args, **kwargs)

        instance = cls._instances[cls]

        # Initialize the instance if it has an initialize method
        if hasattr(instance, "initialize") and callable(
            getattr(instance, "initialize")
        ):
            # Use a lock to prevent concurrent initialization
            if cls not in cls._init_locks:
                cls._init_locks[cls] = asyncio.Lock()

            async with cls._init_locks[cls]:
                # Call initialize if it hasn't been called yet
                if not getattr(instance, "is_initialized", False):
                    init_method = getattr(instance, "initialize")

                    # Check if initialize is async
                    if inspect.iscoroutinefunction(init_method):
                        await init_method()
                    else:
                        init_method()

                    # Mark as initialized if the instance doesn't do it itself
                    if (
                        not hasattr(instance, "is_initialized")
                        or not instance.is_initialized
                    ):
                        setattr(instance, "is_initialized", True)

        return instance

    @classmethod
    def reset_instance(cls):
        """
        Reset the singleton instance.

        This will remove the existing instance, allowing a new one to be created
        on the next call to get_instance().
        """
        if cls in cls._instances:
            # Call shutdown if it exists
            instance = cls._instances[cls]
            if hasattr(instance, "shutdown") and callable(
                getattr(instance, "shutdown")
            ):
                shutdown_method = getattr(instance, "shutdown")

                # Check if shutdown is async
                if inspect.iscoroutinefunction(shutdown_method):
                    # We can't await here, so we'll just call it and hope for the best
                    logger.warning(
                        f"Async shutdown method called synchronously for {cls.__name__}"
                    )
                    asyncio.create_task(shutdown_method())
                else:
                    shutdown_method()

            # Remove the instance
            del cls._instances[cls]

            # Remove the lock if it exists
            if cls in cls._init_locks:
                del cls._init_locks[cls]


class EventBus(AsyncSingleton):
    """Event bus for publishing and subscribing to domain events."""
    
    async def _init(self) -> None:
        """Initialize the event bus."""
        warnings.warn(
            "EventBus from shared.utils.patterns is deprecated and violates architectural principles. "
            "Use HybridMessageBus via get_message_bus() instead. "
            "This will be removed in Sprint-4. "
            "See docs/migration/EVENTBUS_TO_HYBRID_MIGRATION.md for migration guide.",
            DeprecationWarning,
            stacklevel=2
        )
        self._subscribers: Dict[str, List[Callable]] = {}
