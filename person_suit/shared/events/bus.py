"""Minimal EventBus placeholder to satisfy imports during early sprints.
This will be replaced with full implementation in later roadmap phases.
"""
from __future__ import annotations

import warnings
from typing import Any
from typing import Callable
from typing import Coroutine
from typing import Dict
from typing import List
from typing import Optional


class EventBus:
    """A very thin pub/sub bus used only for unit-test collection phase."""

    def __init__(self) -> None:
        warnings.warn(
            "EventBus is deprecated and violates architectural principles. "
            "Use HybridMessageBus via get_message_bus() instead. "
            "This will be removed in Sprint-4. "
            "See docs/migration/EVENTBUS_TO_HYBRID_MIGRATION.md for migration guide.",
            DeprecationWarning,
            stacklevel=2
        )
        self._subscribers: Dict[str, List[Callable[[Any], Coroutine[Any, Any, None]]]] = {}

    async def publish(self, topic: str, payload: Any) -> None:  # noqa: D401
        """Publish payload to all subscribers of *topic*."""
        for handler in self._subscribers.get(topic, []):
            await handler(payload)

    async def subscribe(self, topic: str, handler: Callable[[Any], Coroutine[Any, Any, None]]) -> None:  # noqa: D401
        """Subscribe *handler* coroutine to *topic*."""
        self._subscribers.setdefault(topic, []).append(handler)


# Error types expected by some tests
class EventPublishError(RuntimeError):
    pass


class EventSubscriptionError(RuntimeError):
    pass


# Singleton instance used by tests that import directly
_bus_singleton: Optional[EventBus] = None

def get_event_bus() -> EventBus:  # pragma: no cover – test helper
    warnings.warn(
        "get_event_bus() is deprecated and violates architectural principles. "
        "Use get_message_bus() from person_suit.core.infrastructure.hybrid_message_bus instead. "
        "This will be removed in Sprint-4. "
        "See docs/migration/EVENTBUS_TO_HYBRID_MIGRATION.md for migration guide.",
        DeprecationWarning,
        stacklevel=2
    )
    global _bus_singleton
    if _bus_singleton is None:
        _bus_singleton = EventBus()
    return _bus_singleton 