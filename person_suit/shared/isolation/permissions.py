"""
Permissions Module for Meta-System Isolation Framework (SI-2)

This module provides permission management for the isolation framework,
controlling which systems can interact with each other and what operations
are allowed across system boundaries.

Key functionality includes:
- Permission definition and verification
- Access control for cross-boundary operations
- Permission inheritance and aggregation
- Security context handling
"""

import enum
import logging
from dataclasses import dataclass
from dataclasses import field
from typing import Any
from typing import Dict
from typing import Optional

from . import CommunicationMode
from . import PermissionLevel
from .boundary_manager import SystemIdentifier

# Configure logger
logger = logging.getLogger(__name__)


class OperationType(enum.Enum):
    """Types of operations that can be performed across boundaries."""

    READ = "read"  # Read data from a system
    WRITE = "write"  # Write data to a system
    EXECUTE = "execute"  # Execute an action in a system
    CONNECT = "connect"  # Establish a connection to a system
    MONITOR = "monitor"  # Monitor a system's state
    CONTROL = "control"  # Control a system's operation


@dataclass
class Permission:
    """
    Permission for a specific operation.

    Defines whether a specific operation is allowed and under what
    conditions.

    Attributes:
        operation: Type of operation
        allowed: Whether the operation is allowed
        level: Permission level required
        conditions: Conditions under which the permission applies
        metadata: Additional metadata about the permission
    """

    operation: OperationType
    allowed: bool = False
    level: PermissionLevel = PermissionLevel.NONE
    conditions: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def check(self, context: Dict[str, Any]) -> bool:
        """
        Check if the permission allows an operation in a given context.

        Args:
            context: Context in which to check the permission

        Returns:
            True if the operation is allowed, False otherwise
        """
        # If not allowed at all, return immediately
        if not self.allowed:
            return False

        # Check security level
        if "security_level" in context:
            context_level = context["security_level"]
            if isinstance(context_level, str):
                try:
                    context_level = PermissionLevel[context_level.upper()]
                except KeyError:
                    logger.warning(f"Invalid security level in context: {context_level}")
                    return False

            if context_level < self.level:
                return False

        # Check conditions
        for key, value in self.conditions.items():
            if key not in context:
                return False

            if context[key] != value:
                return False

        return True


@dataclass
class PermissionSet:
    """
    Set of permissions for a specific source-destination pair.

    Defines all permissions for operations between a source and destination
    system.

    Attributes:
        source: Source system identifier
        destination: Destination system identifier
        permissions: Permissions by operation type
        default_allowed: Whether operations are allowed by default
    """

    source: SystemIdentifier
    destination: SystemIdentifier
    permissions: Dict[OperationType, Permission] = field(default_factory=dict)
    default_allowed: bool = False

    def __post_init__(self):
        """Initialize after creation."""
        # Create permissions for all operation types
        for op_type in OperationType:
            if op_type not in self.permissions:
                self.permissions[op_type] = Permission(
                    operation=op_type, allowed=self.default_allowed
                )

    def allow(
        self,
        operation: OperationType,
        level: PermissionLevel = PermissionLevel.NORMAL,
        conditions: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Allow an operation.

        Args:
            operation: Operation to allow
            level: Permission level required
            conditions: Conditions under which the permission applies
        """
        self.permissions[operation] = Permission(
            operation=operation, allowed=True, level=level, conditions=conditions or {}
        )

    def deny(self, operation: OperationType) -> None:
        """
        Deny an operation.

        Args:
            operation: Operation to deny
        """
        if operation in self.permissions:
            self.permissions[operation].allowed = False
        else:
            self.permissions[operation] = Permission(operation=operation, allowed=False)

    def check(self, operation: OperationType, context: Optional[Dict[str, Any]] = None) -> bool:
        """
        Check if an operation is allowed.

        Args:
            operation: Operation to check
            context: Context in which to check the permission

        Returns:
            True if the operation is allowed, False otherwise
        """
        if operation not in self.permissions:
            return self.default_allowed

        return self.permissions[operation].check(context or {})


class PermissionManager:
    """
    Manager for system-wide permissions.

    Manages all permissions between systems and provides methods for checking
    whether operations are allowed.

    Attributes:
        permissions: Permission sets by source-destination pair
        default_allowed: Whether operations are allowed by default
    """

    def __init__(self, default_allowed: bool = False):
        """
        Initialize the permission manager.

        Args:
            default_allowed: Whether operations are allowed by default
        """
        self.permissions: Dict[str, PermissionSet] = {}
        self.default_allowed = default_allowed

    def get_permission_set(
        self,
        source: SystemIdentifier,
        destination: SystemIdentifier,
        create_if_missing: bool = True,
    ) -> Optional[PermissionSet]:
        """
        Get a permission set for a source-destination pair.

        Args:
            source: Source system identifier
            destination: Destination system identifier
            create_if_missing: Whether to create the permission set if it doesn't exist

        Returns:
            Permission set for the source-destination pair, or None if not found
        """
        key = f"{source}:{destination}"

        if key in self.permissions:
            return self.permissions[key]

        if create_if_missing:
            perm_set = PermissionSet(
                source=source,
                destination=destination,
                default_allowed=self.default_allowed,
            )
            self.permissions[key] = perm_set
            return perm_set

        return None

    def allow(
        self,
        source: SystemIdentifier,
        destination: SystemIdentifier,
        operation: OperationType,
        level: PermissionLevel = PermissionLevel.NORMAL,
        conditions: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Allow an operation between two systems.

        Args:
            source: Source system identifier
            destination: Destination system identifier
            operation: Operation to allow
            level: Permission level required
            conditions: Conditions under which the permission applies
        """
        perm_set = self.get_permission_set(source, destination)
        perm_set.allow(operation, level, conditions)

    def deny(
        self,
        source: SystemIdentifier,
        destination: SystemIdentifier,
        operation: OperationType,
    ) -> None:
        """
        Deny an operation between two systems.

        Args:
            source: Source system identifier
            destination: Destination system identifier
            operation: Operation to deny
        """
        perm_set = self.get_permission_set(source, destination)
        perm_set.deny(operation)

    def check(
        self,
        source: SystemIdentifier,
        destination: SystemIdentifier,
        operation: OperationType,
        context: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Check if an operation is allowed between two systems.

        Args:
            source: Source system identifier
            destination: Destination system identifier
            operation: Operation to check
            context: Context in which to check the permission

        Returns:
            True if the operation is allowed, False otherwise
        """
        perm_set = self.get_permission_set(source, destination, create_if_missing=False)

        if perm_set is None:
            return self.default_allowed

        return perm_set.check(operation, context)

    def can_communicate(
        self,
        source: SystemIdentifier,
        destination: SystemIdentifier,
        mode: CommunicationMode = CommunicationMode.SYNCHRONOUS,
        context: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Check if one system can communicate with another.

        Args:
            source: Source system identifier
            destination: Destination system identifier
            mode: Communication mode
            context: Context in which to check the permission

        Returns:
            True if communication is allowed, False otherwise
        """
        # For synchronous/async communication, need READ and WRITE
        # For event-based, only need CONNECT
        if mode == CommunicationMode.EVENT:
            return self.check(source, destination, OperationType.CONNECT, context)
        else:
            return self.check(source, destination, OperationType.WRITE, context) and self.check(
                destination, source, OperationType.READ, context
            )

    def reset(self) -> None:
        """Reset all permissions to defaults."""
        self.permissions.clear()


# Singleton permission manager instance
_PERMISSION_MANAGER: Optional[PermissionManager] = None


def get_permission_manager() -> PermissionManager:
    """
    Get the singleton permission manager instance.

    Returns:
        The global permission manager instance
    """
    global _PERMISSION_MANAGER

    if _PERMISSION_MANAGER is None:
        _PERMISSION_MANAGER = PermissionManager()

    return _PERMISSION_MANAGER


def check_permission(
    source: SystemIdentifier,
    destination: SystemIdentifier,
    operation: OperationType,
    context: Optional[Dict[str, Any]] = None,
) -> bool:
    """
    DEPRECATED: Check if an operation is allowed between two systems.

    This function is deprecated and will be removed in a future version.
    Use the Effect-based security system instead:

    from person_suit.core.effects.security import create_permission_check_effect
    from person_suit.core.effects.interpreter import EffectInterpreterActor

    effect = create_permission_check_effect(
        source_system=str(source),
        destination_system=str(destination),
        operation_type=str(operation),
        security_context=context
    )
    # Execute effect through EffectInterpreter

    Args:
        source: Source system identifier
        destination: destination system identifier
        operation: Operation to check
        context: Context in which to check the permission

    Returns:
        True if the operation is allowed, False otherwise
    """
    import warnings
    warnings.warn(
        "check_permission is deprecated. Use Effect-based security system instead.",
        DeprecationWarning,
        stacklevel=2
    )

    # For backward compatibility, delegate to the old manager
    # This will be removed once all callers are migrated
    manager = get_permission_manager()
    return manager.check(source, destination, operation, context)


def can_communicate(
    source: SystemIdentifier,
    destination: SystemIdentifier,
    mode: CommunicationMode = CommunicationMode.SYNCHRONOUS,
    context: Optional[Dict[str, Any]] = None,
) -> bool:
    """
    Check if one system can communicate with another.

    Args:
        source: Source system identifier
        destination: Destination system identifier
        mode: Communication mode
        context: Context in which to check the permission

    Returns:
        True if communication is allowed, False otherwise
    """
    manager = get_permission_manager()
    return manager.can_communicate(source, destination, mode, context)
