"""
Pattern Detection Events System
==============================

This module provides an event-based notification system for the Pattern Detection Framework,
enabling real-time updates and integration with other system components.

The events system allows for:
1. Publishing pattern detection events to subscribers
2. Subscribing to specific pattern event types
3. Asynchronous event processing
4. Cross-component communication
"""

import asyncio
import logging
import uuid
from dataclasses import dataclass
from dataclasses import field
from datetime import datetime
from enum import Enum
from typing import Any
from typing import Awaitable
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional
from typing import Set
from typing import Union


class PatternEventType(Enum):
    """Types of pattern-related events that can be published."""

    PATTERN_DETECTED = "pattern_detected"
    PATTERN_UPDATED = "pattern_updated"
    PATTERN_VERIFIED = "pattern_verified"
    PATTERN_REJECTED = "pattern_rejected"
    PATTERN_MERGED = "pattern_merged"
    PATTERN_RELATIONSHIP_CREATED = "pattern_relationship_created"
    PATTERN_STORED_IN_MEMORY = "pattern_stored_in_memory"
    PATTERN_RETRIEVED_FROM_MEMORY = "pattern_retrieved_from_memory"
    SIMILAR_PATTERNS_FOUND = "similar_patterns_found"
    ANOMALY_DETECTED = "anomaly_detected"


@dataclass
class PatternEvent:
    """
    Represents an event related to pattern detection.

    Attributes:
        event_id: Unique identifier for the event
        event_type: Type of the event
        pattern_id: ID of the pattern this event is about
        timestamp: When the event occurred
        source: Component that generated the event
        data: Additional event-specific data
        metadata: Additional metadata about the event
    """

    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: PatternEventType = PatternEventType.PATTERN_DETECTED
    pattern_id: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    source: str = "pattern_detection"
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


# Type alias for event handlers
EventHandlerType = Callable[[PatternEvent], Awaitable[None]]


class PatternEventBus:
    """
    Event bus for publishing and subscribing to pattern-related events.

    This class implements the publisher-subscriber pattern for asynchronous
    event processing in the Pattern Detection Framework.
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the pattern event bus.

        Args:
            logger: Optional logger for tracking event operations
        """
        import warnings
        warnings.warn(
            "PatternEventBus is deprecated and violates architectural principles. "
            "Use HybridMessageBus via get_message_bus() instead. "
            "This will be removed in Sprint-4. "
            "See docs/migration/EVENTBUS_TO_HYBRID_MIGRATION.md for migration guide.",
            DeprecationWarning,
            stacklevel=2
        )
        self.logger = logger or logging.getLogger(__name__)

        # Dictionary of event type -> set of handlers
        self._subscribers: Dict[PatternEventType, Set[EventHandlerType]] = {
            event_type: set() for event_type in PatternEventType
        }

        # For wildcard subscriptions (all events)
        self._wildcard_subscribers: Set[EventHandlerType] = set()

        # Event history for debugging and analysis
        self._event_history: List[PatternEvent] = []
        self._max_history_size = 1000

        # Flag to track if the event processing is running
        self._processing = False
        self._event_queue: asyncio.Queue = asyncio.Queue()

    async def start(self) -> None:
        """Start the event processing loop."""
        if self._processing:
            return

        self._processing = True
        asyncio.create_task(self._process_events())
        self.logger.info("Pattern event bus started")

    async def stop(self) -> None:
        """Stop the event processing loop."""
        self._processing = False
        self.logger.info("Pattern event bus stopped")

    def subscribe(
        self, event_type: Optional[PatternEventType], handler: EventHandlerType
    ) -> None:
        """
        Subscribe to a specific type of pattern event.

        Args:
            event_type: Type of event to subscribe to, or None for all events
            handler: Async function to call when the event occurs
        """
        if event_type is None:
            # Subscribe to all events
            self._wildcard_subscribers.add(handler)
            self.logger.debug(f"Handler {handler.__name__} subscribed to all events")
        else:
            # Subscribe to a specific event type
            self._subscribers[event_type].add(handler)
            self.logger.debug(
                f"Handler {handler.__name__} subscribed to {event_type.name}"
            )

    def unsubscribe(
        self, event_type: Optional[PatternEventType], handler: EventHandlerType
    ) -> None:
        """
        Unsubscribe from a specific type of pattern event.

        Args:
            event_type: Type of event to unsubscribe from, or None for all events
            handler: Handler function to remove
        """
        if event_type is None:
            # Unsubscribe from all events
            self._wildcard_subscribers.discard(handler)
            self.logger.debug(
                f"Handler {handler.__name__} unsubscribed from all events"
            )
        else:
            # Unsubscribe from a specific event type
            if event_type in self._subscribers:
                self._subscribers[event_type].discard(handler)
                self.logger.debug(
                    f"Handler {handler.__name__} unsubscribed from {event_type.name}"
                )

    async def publish(self, event: PatternEvent) -> None:
        """
        Publish a pattern event to all subscribers.

        Args:
            event: The event to publish
        """
        # Add to history
        self._add_to_history(event)

        # Add to queue for processing
        await self._event_queue.put(event)
        self.logger.debug(
            f"Event {event.event_type.name} for pattern {event.pattern_id} queued"
        )

    async def _process_events(self) -> None:
        """Process events from the queue."""
        while self._processing:
            try:
                # Get event from queue
                event = await self._event_queue.get()

                # Get handlers for this event type
                handlers = set(self._subscribers.get(event.event_type, set()))

                # Add wildcard subscribers
                handlers.update(self._wildcard_subscribers)

                # Process event with each handler
                if handlers:
                    self.logger.debug(
                        f"Processing event {event.event_type.name} with {len(handlers)} handlers"
                    )

                    # Create tasks for each handler
                    tasks = []
                    for handler in handlers:
                        tasks.append(
                            asyncio.create_task(self._call_handler(handler, event))
                        )

                    # Wait for all handlers to complete
                    if tasks:
                        await asyncio.gather(*tasks, return_exceptions=True)

                # Mark as done
                self._event_queue.task_done()

            except asyncio.CancelledError:
                break

            except Exception as e:
                self.logger.error(f"Error processing event: {str(e)}")

    async def _call_handler(
        self, handler: EventHandlerType, event: PatternEvent
    ) -> None:
        """
        Call an event handler safely.

        Args:
            handler: The handler to call
            event: The event to pass to the handler
        """
        try:
            await handler(event)
        except Exception as e:
            self.logger.error(f"Error in event handler {handler.__name__}: {str(e)}")

    def _add_to_history(self, event: PatternEvent) -> None:
        """
        Add an event to the history.

        Args:
            event: The event to add
        """
        self._event_history.append(event)

        # Trim history if needed
        if len(self._event_history) > self._max_history_size:
            self._event_history = self._event_history[-self._max_history_size :]

    def get_history(self, limit: int = 100) -> List[PatternEvent]:
        """
        Get recent events from history.

        Args:
            limit: Maximum number of events to return

        Returns:
            List of recent events
        """
        return self._event_history[-limit:]


# Global event bus instance for pattern detection
_pattern_event_bus: Optional[PatternEventBus] = None


async def get_pattern_event_bus() -> PatternEventBus:
    """
    Get the global pattern event bus instance.

    Returns:
        The global PatternEventBus instance
    """
    import warnings
    warnings.warn(
        "get_pattern_event_bus() is deprecated and violates architectural principles. "
        "Use get_message_bus() from person_suit.core.infrastructure.hybrid_message_bus instead. "
        "This will be removed in Sprint-4. "
        "See docs/migration/EVENTBUS_TO_HYBRID_MIGRATION.md for migration guide.",
        DeprecationWarning,
        stacklevel=2
    )
    global _pattern_event_bus

    if _pattern_event_bus is None:
        _pattern_event_bus = PatternEventBus()
        await _pattern_event_bus.start()

    return _pattern_event_bus
