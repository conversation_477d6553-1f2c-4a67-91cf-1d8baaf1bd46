"""
Design Patterns for Memory System.

This module provides common design patterns used throughout the memory system,
including the AsyncSingleton pattern for global services.

Related Files:
- events.py: Event system implementation
- system.py: Memory system implementation
"""

import asyncio
from typing import Any
from typing import Dict
from typing import Optional
from typing import Type
from typing import Type<PERSON><PERSON>
from typing import List
from typing import Callable
import warnings

T = TypeVar("T")


class AsyncSingleton:
    """
    AsyncSingleton pattern implementation for global service objects.

    This pattern ensures that only one instance of a class exists and provides
    asynchronous initialization, which is important for services that require
    setup operations that are async in nature.

    Example:
        ```python
        class EventBus(AsyncSingleton):
            async def initialize(self):
                self._subscribers = {}

            async def publish(self, event_type, data=None):
                # Implementation

        # Usage
        event_bus = await EventBus.get_instance()
        ```
    """

    _instances: Dict[Type, Any] = {}
    _init_locks: Dict[Type, asyncio.Lock] = {}

    @classmethod
    async def get_instance(cls: Type[T]) -> T:
        """
        Get the singleton instance of the class.

        If the instance doesn't exist, it will be created and initialized.
        If the instance exists but isn't initialized, it will be initialized.

        Returns:
            The singleton instance of the class
        """
        # Get or create lock for this class
        if cls not in cls._init_locks:
            cls._init_locks[cls] = asyncio.Lock()

        # Use lock to prevent race conditions during initialization
        async with cls._init_locks[cls]:
            # Create instance if it doesn't exist
            if cls not in cls._instances:
                instance = cls.__new__(cls)
                cls._instances[cls] = instance

                # Initialize the instance if it has an initialize method
                if hasattr(instance, "initialize") and callable(instance.initialize):
                    await instance.initialize()

            return cls._instances[cls]

    @classmethod
    def has_instance(cls) -> bool:
        """
        Check if an instance of this class already exists.

        Returns:
            True if an instance exists, False otherwise
        """
        return cls in cls._instances

    @classmethod
    async def reset_instance(cls) -> None:
        """
        Reset the singleton instance of the class.

        If the instance has a shutdown method, it will be called.
        Then the instance will be removed.

        This is primarily useful for testing.
        """
        if cls in cls._instances:
            instance = cls._instances[cls]

            # Call shutdown if it exists
            if hasattr(instance, "shutdown") and callable(instance.shutdown):
                await instance.shutdown()

            # Remove instance
            del cls._instances[cls]


class Registry:
    """
    Registry pattern implementation.

    This pattern provides a central place to register and retrieve components,
    supporting dependency lookup and inversion of control.

    Example:
        ```python
        # Register components
        registry = Registry()
        registry.register("event_manager", event_manager)
        registry.register("memory_system", memory_system)

        # Retrieve components
        event_manager = registry.get("event_manager")
        ```
    """

    def __init__(self):
        """Initialize the registry."""
        self._components: Dict[str, Any] = {}

    def register(self, name: str, component: Any) -> None:
        """
        Register a component.

        Args:
            name: The name to register the component under
            component: The component to register
        """
        self._components[name] = component

    def get(self, name: str) -> Optional[Any]:
        """
        Get a registered component.

        Args:
            name: The name of the component to get

        Returns:
            The component, or None if not found
        """
        return self._components.get(name)

    def has(self, name: str) -> bool:
        """
        Check if a component is registered.

        Args:
            name: The name of the component to check

        Returns:
            True if the component is registered, False otherwise
        """
        return name in self._components

    def remove(self, name: str) -> None:
        """
        Remove a registered component.

        Args:
            name: The name of the component to remove
        """
        if name in self._components:
            del self._components[name]


class MemorySystemRegistry(Registry, AsyncSingleton):
    """
    Registry for the memory system components.

    This combines the Registry pattern with the AsyncSingleton pattern
    to provide a global registry for memory system components.
    """

    async def initialize(self) -> None:
        """Initialize the registry."""
        self._components = {}

    async def shutdown(self) -> None:
        """Shut down all components in the registry."""
        for name, component in list(self._components.items()):
            if hasattr(component, "shutdown") and callable(component.shutdown):
                await component.shutdown()

        self._components.clear()


class EventBus(AsyncSingleton):
    """Event bus for publishing and subscribing to domain events."""
    
    async def _init(self) -> None:
        """Initialize the event bus."""
        warnings.warn(
            "EventBus from persona_core.memory.patterns is deprecated and violates architectural principles. "
            "Use HybridMessageBus via get_message_bus() instead. "
            "This will be removed in Sprint-4. "
            "See docs/migration/EVENTBUS_TO_HYBRID_MIGRATION.md for migration guide.",
            DeprecationWarning,
            stacklevel=2
        )
        self._subscribers: Dict[str, List[Callable]] = {}
