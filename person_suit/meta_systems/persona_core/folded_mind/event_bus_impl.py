"""
Event Bus Implementation Module for Folded Mind Architecture

This module provides a complete event bus implementation for the Folded Mind architecture,
enabling asynchronous communication between components through a publish-subscribe pattern.

The implementation supports:
- Topic-based event routing
- Asynchronous event handling
- Event filtering
- Event prioritization
- Event history tracking

Related files:
- person_suit/meta_systems/persona_core/folded_mind/event_bus_impl.py - Main module file
- person_suit/meta_systems/persona_core/folded_mind/__init__.py - Core imports
- person_suit/core/application/interfaces/events.py - Core event interfaces

Dependencies:
- Python 3.8+
- Standard library
- person_suit.core.application.interfaces.events
"""

import asyncio
import logging
import threading
import time
import uuid
from collections import defaultdict
from dataclasses import dataclass
from dataclasses import field
from datetime import datetime
from datetime import timezone
from enum import Enum
from typing import Any
from typing import AsyncGenerator
from typing import Awaitable
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional
from typing import Set
from typing import Union

# Import the new translator
from person_suit.core.infrastructure.dual_wave.translation import (
    translate_infom_to_unified_information,
)
from person_suit.shared.information.core import Infom

# Import core event interfaces
try:
    # Import provider interfaces
    from person_suit.core.application.interfaces.event_providers import IEventCapabilityProvider
    from person_suit.core.application.interfaces.event_providers import IEventCryptoProvider
    from person_suit.core.application.interfaces.event_providers import IEventPersistenceProvider
    from person_suit.core.application.interfaces.events_interface import BaseEvent
    from person_suit.core.application.interfaces.events_interface import BaseEventData
    from person_suit.core.application.interfaces.events_interface import Capability
    from person_suit.core.application.interfaces.events_interface import CryptoMetadata
    from person_suit.core.application.interfaces.events_interface import EventFilter
    from person_suit.core.application.interfaces.events_interface import EventHandler
    from person_suit.core.application.interfaces.events_interface import EventOptimizationConfig
    from person_suit.core.application.interfaces.events_interface import EventPriority
    from person_suit.core.application.interfaces.events_interface import EventTopic
except ImportError:
    # Fallback definitions for standalone operation
    from enum import Enum
    from enum import auto

    class EventTopic(Enum):
        """Fallback event topics if core imports fail."""

        SYSTEM = auto()
        MEMORY = auto()
        COGNITIVE = auto()
        EMOTIONAL = auto()
        CUSTOM = auto()

    @dataclass
    class BaseEventData:
        """Base class for all event data payloads."""

        source_component: Optional[str] = None
        timestamp: datetime = field(default_factory=datetime.now)

    @dataclass(frozen=True)
    class BaseEvent:
        """Base wrapper class for all events."""

        topic: EventTopic
        data: BaseEventData
        event_id: str = field(default_factory=lambda: str(uuid.uuid4()))

    @dataclass
    class EventOptimizationConfig:
        """Configuration for event bus optimization."""

        batch_size: int = 1
        enable_m3: bool = False
        priority_queuing: bool = False

    # Define basic Protocol fallbacks if core interfaces aren't available
    class IEventPersistenceProvider(Protocol):
        pass

    class IEventCryptoProvider(Protocol):
        pass

    class IEventCapabilityProvider(Protocol):
        pass

    Capability = TypeVar("Capability")  # Keep placeholder
    CryptoMetadata = Dict[str, Any]  # Keep placeholder

# Configure logging
logger = logging.getLogger(__name__)

# Type definitions
EventHandler = Callable[[BaseEvent], Awaitable[None]]
EventFilter = Callable[[BaseEvent], bool]


@dataclass
class EventSubscription:
    """Represents a subscription to events."""

    handler: EventHandler
    topics: Set[EventTopic] = field(default_factory=set)
    filter_func: Optional[EventFilter] = None
    priority: EventPriority = EventPRIO_NORMAL
    subscriber_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    handler_identity: Any = None


class EventBusImpl:
    """
    Provides a complete event bus implementation for the Folded Mind architecture.

    This class implements the event bus pattern, allowing components to publish
    events and subscribe to events of interest.
    """

    def __init__(
        self,
        processing_strategy: str = "SEQUENTIAL",
        max_retries: int = 3,
        retry_delay: float = 1.0,
        broadcast_timeout: float = 5.0,
        process_timeout: float = 10.0,
        custom_providers: Optional[List[BaseProvider]] = None,
    ):
        """
        Initialize the EventBusImpl.

        Args:
            processing_strategy: The processing strategy for events
                                 ("SEQUENTIAL", "PARALLEL", "PRIORITY").
            max_retries: Maximum number of retries for event processing.
            retry_delay: Delay between retries in seconds.
            broadcast_timeout: Timeout for broadcasting events.
            process_timeout: Timeout for processing individual events.
            custom_providers: Optional list of custom event providers.
        """
        import warnings
        warnings.warn(
            "EventBusImpl is deprecated and violates architectural principles. "
            "Use HybridMessageBus via get_message_bus() instead. "
            "This will be removed in Sprint-4. "
            "See docs/migration/EVENTBUS_TO_HYBRID_MIGRATION.md for migration guide.",
            DeprecationWarning,
            stacklevel=2
        )
        logger.debug("Initializing EventBusImpl with enhanced providers")
        self.config = EventOptimizationConfig()

        # Store injected providers
        self._persistence_provider = None
        self._crypto_provider = None
        self._capability_provider = None

        # Validate provider presence if features are enabled in config
        if self.config.persistence_enabled and not self._persistence_provider:
            logger.warning("Persistence enabled in config, but no provider injected.")
        if self.config.crypto_enabled and not self._crypto_provider:
            logger.warning("Crypto enabled in config, but no provider injected.")
        if self.config.capability_check_enabled and not self._capability_provider:
            logger.warning(
                "Capability check enabled in config, but no provider injected."
            )

        # Subscriptions by topic
        self._topic_subscriptions: Dict[EventTopic, List[EventSubscription]] = (
            defaultdict(list)
        )

        # Subscriptions to all events
        self._global_subscriptions: List[EventSubscription] = []

        # Event history (potentially smaller if persistence is enabled)
        self._event_history: List[BaseEvent] = []
        self._max_history_size = self.config.max_history_size

        # Locks for thread safety
        self._subscription_lock = threading.RLock()
        self._history_lock = threading.RLock()

        # Event queue for async processing
        self._event_queue: Optional[asyncio.Queue] = None
        self._processor_task: Optional[asyncio.Task] = None
        self._running = False

        # Statistics
        self._events_processed = 0
        self._events_published = 0
        self._last_event_time = 0.0

        self.initialized = False
        logger.info("EventBusImpl instance created")

    async def start(self) -> None:
        """
        Start the event processor and initialize providers.
        """
        if self._running:
            logger.warning("EventBusImpl already running")
            return

        logger.info("Starting EventBusImpl and initializing providers...")
        try:
            # Initialize providers first
            if self._persistence_provider and self.config.persistence_enabled:
                await self._persistence_provider.initialize()
                logger.info("Persistence provider initialized.")
            if self._crypto_provider and self.config.crypto_enabled:
                await self._crypto_provider.initialize()
                logger.info("Crypto provider initialized.")
            if self._capability_provider and self.config.capability_check_enabled:
                await self._capability_provider.initialize()
                logger.info("Capability provider initialized.")

            # Start the event processing queue and task
            self._event_queue = asyncio.Queue(maxsize=self.config.max_queue_size)
            self._running = True
            self._processor_task = asyncio.create_task(
                self._process_events(), name="event_processor"
            )
            self.initialized = True
            logger.info("EventBusImpl event processor started successfully.")

        except Exception as e:
            logger.exception(
                "Failed to start EventBusImpl or initialize providers.", exc_info=True
            )
            # Attempt partial shutdown of any initialized providers
            await self._shutdown_providers()
            self._running = False
            self.initialized = False
            raise RuntimeError("EventBusImpl failed to start.") from e

    async def stop(self) -> None:
        """
        Stop the event processor and shutdown providers.
        """
        if not self._running:
            logger.warning("EventBusImpl not running")
            return

        logger.info(
            "Stopping EventBusImpl event processor and shutting down providers..."
        )
        self._running = False

        # Signal and wait for processor task to finish
        if self._event_queue:
            try:
                # Put sentinel None if queue is not full or size is 0
                if (
                    self.config.max_queue_size == 0
                    or self._event_queue.qsize() < self.config.max_queue_size
                ):
                    self._event_queue.put_nowait(None)
                else:
                    logger.warning(
                        "Event queue is full, cannot add sentinel. Processor might take longer to stop."
                    )
            except asyncio.QueueFull:
                logger.warning("Event queue is full on stop, cannot add sentinel.")

        if self._processor_task:
            try:
                await asyncio.wait_for(self._processor_task, timeout=10.0)
            except asyncio.TimeoutError:
                logger.warning(
                    "Event processor task did not complete in time, cancelling"
                )
                self._processor_task.cancel()
                try:
                    await self._processor_task
                except asyncio.CancelledError:
                    logger.info("Event processor task cancelled successfully.")
                except Exception as e:
                    logger.exception(
                        f"Exception during event processor task cancellation: {e}",
                        exc_info=True,
                    )
            except Exception as e:
                logger.exception(
                    f"Exception waiting for event processor task: {e}", exc_info=True
                )
            finally:
                self._processor_task = None

        # Shutdown providers
        await self._shutdown_providers()

        self.initialized = False
        logger.info("EventBusImpl stopped successfully.")

    async def _shutdown_providers(self) -> None:
        """Helper method to shut down initialized providers."""
        logger.debug("Shutting down providers...")
        try:
            if self._persistence_provider and self.config.persistence_enabled:
                await self._persistence_provider.shutdown()
                logger.info("Persistence provider shut down.")
        except Exception:
            logger.exception("Error shutting down persistence provider", exc_info=True)
        try:
            if self._crypto_provider and self.config.crypto_enabled:
                await self._crypto_provider.shutdown()
                logger.info("Crypto provider shut down.")
        except Exception:
            logger.exception("Error shutting down crypto provider", exc_info=True)
        try:
            if self._capability_provider and self.config.capability_check_enabled:
                await self._capability_provider.shutdown()
                logger.info("Capability provider shut down.")
        except Exception:
            logger.exception("Error shutting down capability provider", exc_info=True)

    async def publish(self, event: BaseEvent) -> None:
        """
        Publish an event to the event bus, performing security checks and persistence.
        """
        if not self._running:
            logger.error(
                f"Attempted to publish event {event.event_id} while bus is not running. Dropping."
            )
            return

        try:
            # 1. Capability Check (Publish)
            if self.config.capability_check_enabled and self._capability_provider:
                can_publish = await self._capability_provider.verify_publish_capability(
                    event.data.originator_capability
                )
                if not can_publish:
                    logger.warning(
                        f"Publish capability check failed for event {event.event_id} "
                        f"(Originator: {event.data.source_component}). Dropping event."
                    )
                    return

            # 2. Cryptographic Signing
            if self.config.crypto_enabled and self._crypto_provider:
                try:
                    if not hasattr(event.data, "crypto_metadata"):
                        logger.error(
                            f"Event data for {event.event_id} lacks 'crypto_metadata' field. Cannot sign."
                        )
                    else:
                        crypto_meta = await self._crypto_provider.sign_event_data(
                            event.data
                        )
                        event.data.crypto_metadata = crypto_meta
                        logger.debug(f"Event data {event.event_id} signed.")
                except Exception:
                    logger.exception(
                        f"Failed to sign event data {event.event_id}. Proceeding without signature.",
                        exc_info=True,
                    )
                    if hasattr(event.data, "crypto_metadata"):
                        event.data.crypto_metadata = None

            # 3. Persistence (Save before queuing/processing)
            if self.config.persistence_enabled and self._persistence_provider:
                try:
                    await self._persistence_provider.save_event(event)
                    logger.debug(f"Event {event.event_id} persisted.")
                except Exception:
                    logger.exception(
                        f"Failed to persist event {event.event_id}. Event might be lost if processing fails.",
                        exc_info=True,
                    )

            # 4. Add to history (optional, maybe only if persistence off?)
            self._add_to_history(event)

            # 5. Update statistics
            self._events_published += 1
            self._last_event_time = time.time()

            # 6. Add to queue for processing
            if self._event_queue:
                try:
                    await self._event_queue.put(event)
                    logger.debug(
                        f"Event {event.event_id} ({event.event_type} on {event.topic}) queued for processing."
                    )
                except Exception:
                    logger.exception(
                        f"Failed to queue event {event.event_id}", exc_info=True
                    )
            else:
                logger.error(
                    f"Event queue not available for event {event.event_id}. Event dropped."
                )

        except Exception:
            logger.exception(
                f"Unexpected error during event publishing {event.event_id}",
                exc_info=True,
            )

    def subscribe(
        self,
        handler: EventHandler,
        topics: Optional[Union[EventTopic, List[EventTopic]]] = None,
        filter_func: Optional[EventFilter] = None,
        priority: Optional[EventPriority] = None,
        handler_identity: Any = None,
    ) -> str:
        """
        Subscribe to events.

        Args:
            handler: Async function to call when matching events are published.
            topics: Specific topics or list of topics to subscribe to (None for all topics).
            filter_func: Optional function to filter events (returns True if event matches).
            priority: Priority for handling events (overrides config default).
            handler_identity: An identifier for the handler used for capability checks.
                               If None, capability checks requiring identity might fail.

        Returns:
            Subscription ID that can be used to unsubscribe.
        """
        sub_priority = (
            priority if priority is not None else self.config.default_priority
        )

        with self._subscription_lock:
            # Create subscription
            subscription = EventSubscription(
                handler=handler,
                priority=sub_priority,
                filter_func=filter_func,
                handler_identity=handler_identity,
            )

            if topics is None:
                # Subscribe to all events
                subscription.topics = set(EventTopic)
                self._global_subscriptions.append(subscription)
                self._sort_subscriptions(self._global_subscriptions)
                logger.debug(
                    f"Added global subscription: {subscription.subscriber_id} (Priority: {sub_priority.name})"
                )
            else:
                topic_set = set(topics) if isinstance(topics, list) else {topics}
                subscription.topics = topic_set
                for topic in topic_set:
                    self._topic_subscriptions[topic].append(subscription)
                    self._sort_subscriptions(self._topic_subscriptions[topic])
                logger.debug(
                    f"Added subscription {subscription.subscriber_id} for topics {topic_set} (Priority: {sub_priority.name})"
                )

            return subscription.subscriber_id

    def unsubscribe(self, subscription_id: str) -> bool:
        """
        Unsubscribe a handler using its subscription ID.
        """
        found = False
        with self._subscription_lock:
            # Check global subscriptions
            new_global_subs = [
                sub
                for sub in self._global_subscriptions
                if sub.subscriber_id != subscription_id
            ]
            if len(new_global_subs) < len(self._global_subscriptions):
                self._global_subscriptions = new_global_subs
                found = True

            # Check topic-specific subscriptions
            for topic in list(self._topic_subscriptions.keys()):
                original_len = len(self._topic_subscriptions[topic])
                self._topic_subscriptions[topic] = [
                    sub
                    for sub in self._topic_subscriptions[topic]
                    if sub.subscriber_id != subscription_id
                ]
                if len(self._topic_subscriptions[topic]) < original_len:
                    found = True
                # Clean up empty topic lists
                if not self._topic_subscriptions[topic]:
                    del self._topic_subscriptions[topic]

        if found:
            logger.debug(f"Subscription {subscription_id} removed.")
        else:
            logger.warning(
                f"Subscription ID {subscription_id} not found for unsubscribe."
            )
        return found

    def _sort_subscriptions(self, sub_list: List[EventSubscription]):
        """Sorts a list of subscriptions by priority (highest first)."""
        # Sorts in place, highest priority value first
        sub_list.sort(key=lambda sub: sub.priority.value, reverse=True)

    def get_event_history(self, limit: Optional[int] = None) -> List[BaseEvent]:
        """Returns a copy of the recent event history."""
        with self._history_lock:
            # Return a copy to prevent external modification
            history_copy = list(self._event_history)

        if limit is not None and limit >= 0:
            return history_copy[-limit:]
        else:
            return history_copy

    def clear_event_history(self) -> None:
        """Clears the in-memory event history."""
        with self._history_lock:
            self._event_history = []
        logger.debug("In-memory event history cleared.")

    def get_statistics(self) -> Dict[str, Any]:
        """Returns current operational statistics."""
        queue_size = self._event_queue.qsize() if self._event_queue else 0
        return {
            "events_published": self._events_published,
            "events_processed": self._events_processed,
            "current_queue_size": queue_size,
            "is_running": self._running,
            "last_event_time": (
                datetime.fromtimestamp(self._last_event_time, tz=timezone.utc)
                if self._last_event_time
                else None
            ),
            "subscription_count": sum(
                len(subs) for subs in self._topic_subscriptions.values()
            )
            + len(self._global_subscriptions),
            "config": self.config,
        }

    def _add_to_history(self, event: BaseEvent) -> None:
        """Adds an event to the in-memory history, respecting max size."""
        if not self.config.persistence_enabled or self._max_history_size > 0:
            with self._history_lock:
                self._event_history.append(event)
                if len(self._event_history) > self._max_history_size > 0:
                    self._event_history = self._event_history[-self._max_history_size :]

    async def _process_events(self) -> None:
        """The main loop that processes events from the queue."""
        logger.info("Event processor task started.")
        while self._running:
            try:
                event = await self._event_queue.get()

                if event is None:
                    logger.info("Sentinel received, stopping event processor.")
                    break

                logger.debug(
                    f"Processing event {event.event_id} ({event.event_type} on {event.topic})"
                )
                await self._dispatch_event(event)
                self._event_queue.task_done()
                self._events_processed += 1

            except asyncio.CancelledError:
                logger.info("Event processor task cancelled.")
                break
            except Exception:
                logger.exception("Error during event processing loop.", exc_info=True)
                await asyncio.sleep(0.1)

        logger.info("Event processor task finished.")

    async def _dispatch_event(self, event: BaseEvent) -> None:
        """
        Dispatch a single event to all relevant subscribers.
        Handles topic-based and global subscriptions.
        This is the integration point for CAW information model translation.
        """
        # CAW Information Model Translation
        if hasattr(event.data, "infom") and isinstance(event.data.infom, Infom):
            try:
                # Translate the lightweight Infom DTO to the rich UnifiedInformation object
                unified_info = translate_infom_to_unified_information(event.data.infom)
                
                # Replace the infom on the event data payload
                event.data.infom = unified_info
                logger.debug(f"Translated Infom to UnifiedInformation for Event ID: {event.event_id}")
            except Exception as e:
                logger.error(
                    f"Failed to translate Infom for Event ID {event.event_id}: {e}",
                    exc_info=True,
                )
                # Depending on policy, we might halt or continue with the original event
                # For now, we continue with the untranslated event

        # --- Original dispatch logic ---
        # Get subscribers for the event's topic
        with self._subscription_lock:
            topic_subscribers = self._topic_subscriptions.get(event.topic, [])

        # Combine and call handlers
        relevant_subs = topic_subscribers + self._global_subscriptions

        for subscription in relevant_subs:
            if subscription.filter_func and not subscription.filter_func(event):
                logger.debug(
                    f"Event {event.event_id} filtered out for subscriber {subscription.subscriber_id}"
                )
                continue

            if self.config.crypto_enabled and self._crypto_provider:
                if event.data.crypto_metadata:
                    try:
                        is_valid = await self._crypto_provider.verify_event_data(
                            event.data, event.data.crypto_metadata
                        )
                        if not is_valid:
                            logger.warning(
                                f"Event {event.event_id} signature verification failed for subscriber "
                                f"{subscription.subscriber_id}. Skipping handler."
                            )
                            continue
                    except Exception:
                        logger.exception(
                            f"Error verifying signature for event {event.event_id}, subscriber "
                            f"{subscription.subscriber_id}. Skipping handler.",
                            exc_info=True,
                        )
                        continue
                else:
                    logger.warning(
                        f"Crypto enabled, but event {event.event_id} has no crypto metadata. "
                        f"Skipping handler for subscriber {subscription.subscriber_id}."
                    )
                    continue

            if self.config.capability_check_enabled and self._capability_provider:
                try:
                    can_handle = (
                        await self._capability_provider.verify_handle_capability(
                            subscription.handler_identity,
                            event.data.required_capabilities,
                        )
                    )
                    if not can_handle:
                        logger.warning(
                            f"Handler capability check failed for event {event.event_id}, subscriber "
                            f"{subscription.subscriber_id} (Needs: {event.data.required_capabilities}). "
                            f"Skipping handler."
                        )
                        continue
                except Exception:
                    logger.exception(
                        f"Error checking handle capability for event {event.event_id}, subscriber "
                        f"{subscription.subscriber_id}. Skipping handler.",
                        exc_info=True,
                    )
                    continue

            await self._safe_call_handler(subscription, event)

    async def _safe_call_handler(
        self, subscription: EventSubscription, event: BaseEvent
    ) -> None:
        """Safely calls a single event handler and logs errors."""
        try:
            logger.debug(
                f"Calling handler {subscription.handler_identity or subscription.subscriber_id} for event {event.event_id}"
            )
            await subscription.handler(event)
        except Exception:
            logger.exception(
                f"Error in event handler {subscription.handler_identity or subscription.subscriber_id} "
                f"for event {event.event_id} ({event.event_type})",
                exc_info=True,
            )

    async def replay_events(self, **kwargs) -> AsyncGenerator[BaseEvent, None]:
        """
        Retrieves and yields historical events based on criteria (requires persistence provider).
        NOTE: This currently only yields events, it does not re-publish them to the bus.
        """
        if not self.config.persistence_enabled or not self._persistence_provider:
            logger.error(
                "Cannot replay events: Persistence is disabled or no provider is configured."
            )
            # Simply return and yield nothing if persistence is off.
            # The 'async for' loop below will handle an empty sequence correctly.
            return
            # The old code attempted to return an empty generator object, which is invalid syntax
            # within an async generator function itself.
            # async def empty_generator():
            #      if False: yield
            # return empty_generator() # <- This is invalid

        logger.info(f"Replaying events with criteria: {kwargs}")
        try:
            # Directly iterate and yield from the provider's async generator
            async for event in self._persistence_provider.retrieve_events(**kwargs):
                yield event
        except Exception:
            logger.exception("Error retrieving events for replay.", exc_info=True)
            # Optionally raise or yield a specific error event
            # For now, just log and stop yielding.


async def process_event_bus_impl(event_bus: EventBusImpl, event: BaseEvent) -> None:
    """Helper function to process a single event through the event bus."""
    logger.warning(
        "Direct processing via process_event_bus_impl is likely deprecated; use publish()."
    )
    await event_bus._dispatch_event(event)


# Define what this module exports
__all__ = [
    "EventBusImpl",
    "EventPriority",
    "EventSubscription",
    "EventHandler",
    "EventFilter",
    "process_event_bus_impl",
]
