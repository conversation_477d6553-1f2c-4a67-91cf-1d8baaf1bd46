#!/usr/bin/env python3
"""
CAW Kernel Smoke Test
====================

Standalone smoke test for CI/CD pipeline.
Tests that the CAW kernel can boot without errors.

Exit codes:
- 0: Success
- 1: Failure
"""

import sys
import asyncio
from pathlib import Path

# Add person_suit to path
package_dir = Path(__file__).parent.parent.absolute()
if str(package_dir) not in sys.path:
    sys.path.insert(0, str(package_dir))

from person_suit.core.infrastructure.canonical_bootstrap import CanonicalBootstrap

async def main():
    """Run smoke test."""
    try:
        # Use CanonicalBootstrap for system initialization
        bootstrap = CanonicalBootstrap()
        await bootstrap.start(stay_alive=False)
        print("✓ CAW Kernel initialized")
        
        # Test basic functionality - get actor system from bootstrap
        actor_system = bootstrap._actor_system  
        if actor_system:
            print("✓ Actor system available")
        
        # Cleanup
        await bootstrap.shutdown()
        print("✓ Clean shutdown completed")
        
        return 0
    except Exception as e:
        print(f"✗ Kernel boot failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main())) 