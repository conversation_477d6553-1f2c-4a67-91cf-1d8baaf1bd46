# Entry-Point & Bootstrap Audit (2025-06-25)

This document fulfils Epic 0 of the *Bootstrap & Messaging Unification* roadmap – providing a factual inventory of all entry-points, bootstrap classes, and direct `HybridMessageBus` constructors currently present in the repository.

> Generated automatically via grep search; see `archive/audit_raw/` for raw lists.

## Artefacts

| Artefact | Path | Generated-By |
|----------|------|--------------|
| Direct bus constructors list | `archive/audit_raw/bus_constructors.txt` | `grep -R "[^a-zA-Z0-9_]HybridMessageBus("` |
| Bootstrap-like classes list  | `archive/audit_raw/bootstraps.txt`      | `grep -R "class .*Bootstrap"` |
| Entry-point scripts list     | `archive/audit_raw/entrypoints.txt`     | `grep -R "if __name__ == "__main__""` |

---

## Quick Statistics

* Direct `HybridMessageBus()` occurrences: **24**
* Bootstrap-like classes discovered: **4**
* Files containing `if __name__ == "__main__"`: **485**

---

## Risk Analysis (initial)

1. **Multiple bus instances** – presence of direct constructors in tests and scripts risks bypassing singleton enforcement.
2. **Duplicate entry-points** – numerous `__main__` blocks hinder guarantee of a single canonical startup path.
3. **Legacy bootstraps** – any non-canonical `*Bootstrap` class may spin up actors outside standard lifecycle management.

Mitigation actions will be tracked under Epic 1 and Epic 2 (`roadmaps/bootstrap_unification_roadmap.md`).

---

## Next Steps

1. Manually classify each bootstrap class (keep vs delete).
2. Tag all redundant entry-point files with `REVERT-ME:EPIC-2` until migrated or removed.
3. Annotate dependency graph (to be stored as `docs/refactor/owner_graph_annotated.mmd`).
4. Open GitHub issues (`EPIC-1-XX`) for every deletion/refactor action.

---

## Bootstrap Classification (2025-06-25)

| Module & Class | Purpose (observed) | Status |
|----------------|--------------------|--------|
| `person_suit/main.py::CanonicalBootstrap` | Canonical startup orchestrator used by `python -m person_suit` | **KEEP** (canonical) |
| `person_suit/core/actors/__init__.py::ActorSystemBootstrap` | Legacy per-test actor system helper (instantiated 6× in tests/tools) | **REVERT-ME:EPIC-2** |
| `person_suit/core/infrastructure/bootstrap.py::SystemBootstrap` | Legacy umbrella bootstrap; wrapper around CanonicalBootstrap | **REVERT-ME:EPIC-2** |
| `person_suit/core/bootstrap/effects.py::_EffectsBootstrapFeature` | Internal feature flag container (not a full system bootstrap) | **KEEP** (utility) |

Inline `REVERT-ME:EPIC-2` tags added to redundant classes to flag deletion in Epic 2. 

---

## Current Status (2025-06-25 - After Consolidation)

### ✅ Bootstrap Classes - COMPLETED
All 4 bootstrap classes have been appropriately handled:
- **ActorSystemBootstrap** - Removed from codebase
- **SystemBootstrap** - Removed (entire file deleted)
- **_EffectsBootstrapFeature** - Kept (utility class, not a system bootstrap)
- **CanonicalBootstrap** - Kept (the one canonical bootstrap)

### ✅ Direct HybridMessageBus() Calls - COMPLETED
- **person_suit/**: 0 violations - All use `get_message_bus()` factory
- **Scripts/Tests**: Still have violations but outside core codebase
- **CI Gate**: Gate 1 PASSES - no direct constructors in production code

### ⚠️ Multiple Entry Points - PARTIAL PROGRESS
**Current State**: 
- **person_suit/**: 28+ files with `if __name__ == "__main__":`
  - Mostly in io_layer interfaces and test files
  - Core infrastructure properly consolidated
- **Scripts**: 100+ standalone scripts (outside person_suit/)
- **Examples**: 50+ demo files (outside person_suit/)
- **Tests**: Many test files with main blocks
- **Action Required**: 
  - Remove/consolidate the 28 entry points in person_suit/
  - Keep only `person_suit/main.py` and `person_suit/__main__.py`
  - Convert CLI interfaces to proper commands

### Recommended Next Steps

1. **Epic 1 Completion**: Fix remaining HybridMessageBus() direct calls
   - Create Ruff rule PS005 to prevent new violations
   - Mass refactor using `scripts/maintenance/fix_get_message_bus_calls.py`

2. **Epic 4 Planning**: Address 485 entry points
   - Categorize by purpose (demos, tests, utilities)
   - Convert to proper CLI commands under `person_suit` command
   - Remove unnecessary main blocks 