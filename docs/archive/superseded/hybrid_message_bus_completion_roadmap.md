# Hybrid Message Bus Completion Roadmap (v2 - CAW-Enhanced)

> Status: **Active** – Updated 2025-06-22 to incorporate WaveTrace gaps and CAW enhancement findings.
> 
> ⚠️ **CRITICAL PREREQUISITE**: Thread-to-Actor Migration ✅ **COMPLETED** (2025-01-25)

This roadmap converts the **Hybrid Message Bus** from today's partial implementation to **full production-ready** status with comprehensive WaveTrace observability and CAW principle verification. Work is organized into **4 phases** with multiple sub-sprints each.

---
## 0 - Baseline Reality Snapshot

### Thread-to-Actor Migration Status ✅ **COMPLETED**
- All 16 thread-based files successfully deprecated
- Foundation actors fully integrated and supervised
- Zero threads in core (verified by grep)
- New thread-free metrics system operational

### Bus Implementation Status (2025-06-22)

* `BusKernel`, ACF, security, provenance middleware **operational**
* Integer-scaled fidelity throughout (0-1,000,000 scale)
* Differential context delta events emitting (`sys.context.delta`)
* `EffectInterpreter` present but underutilized
* **WaveTrace gaps**: No trace context propagation, actor metrics, or CAW branch tracking
* Native provenance backend complete with segment storage

### Critical Findings from Load Analysis

* **1.4% success under extreme load is intentional** - demonstrates proper load shedding
* ACF system adapts at microsecond scale with integer arithmetic
* Priority ordering maintained even under 480K message floods
* All CAW principles verified under stress conditions

---
## Phase 1: Core Infrastructure Hardening (Weeks 1-3)

### Objectives
- Complete CEE (Command-Effect-Event) flow
- Full WaveTrace observability
- Production-grade provenance with trace context

### Sprint 1.1 – CEE Backbone & Registry (Days 1-7)

#### Detailed Tasks
| Day | Tasks |
|-----|-------|
| 1-2 | **Command Handler Registry**<br/>• Create `CommandHandlerRegistry` with auto-subscription<br/>• Implement `@command_handler` decorator with priority support<br/>• Unit tests for registration and routing |
| 3-4 | **Memory Encoder Service**<br/>• Pure function returning `WriteDatabaseEffect`<br/>• DI registration and fast unit tests<br/>• No direct I/O in handler |
| 5 | **Effect Deserialization**<br/>• Factory pattern for Effect types<br/>• Metrics for deserialization errors<br/>• Integration with interpreter |
| 6-7 | **Developer Experience**<br/>• VS Code snippets and templates<br/>• Documentation with Mermaid diagrams<br/>• Asciinema demo recording |

#### Acceptance Criteria
- ✅ Memory encode command → effect → event flow working
- ✅ 90%+ code coverage on handlers package
- ✅ Zero direct I/O violations (Ruff rule enforced)

### Sprint 1.2 – WaveTrace Enhancement (Days 8-14)

#### Detailed Tasks
| Day | Tasks |
|-----|-------|
| 8-9 | **Trace Context in Messages**<br/>• Add trace_id, span_id, parent_span_id to HybridMessage<br/>• Context propagation through message chains<br/>• Backward compatibility maintained |
| 10-11 | **Actor Metrics Integration**<br/>• Mailbox depth recording in DecoupledActor<br/>• Actor lifecycle events (start/stop/restart)<br/>• Supervisor metrics export |
| 12 | **CAW Branch Tracking**<br/>• Record wave vs particle processing decisions<br/>• Fidelity adaptation reasons<br/>• Effect strategy selection |
| 13-14 | **Enhanced Provenance Schema**<br/>• Implement EnhancedProvenanceRecord<br/>• Migration for existing records<br/>• Query API for trace reconstruction |

#### Acceptance Criteria
- ✅ 100% of messages have trace context
- ✅ Actor mailbox depths visible in WaveTrace
- ✅ CAW computational branches recorded
- ✅ End-to-end trace reconstruction working

### Sprint 1.3 – Native Provenance Hardening (Days 15-21)

#### Detailed Tasks
| Day | Tasks |
|-----|-------|
| 15-16 | **Segment Format v2**<br/>• Add trace context to header<br/>• Implement differential compression<br/>• Backward compatible reader |
| 17-18 | **Query Engine**<br/>• Index by trace_id and span_id<br/>• Time-range queries optimized<br/>• Parquet export for analytics |
| 19 | **Crash Recovery**<br/>• Graceful segment recovery<br/>• Checksum validation<br/>• Automatic repair mode |
| 20-21 | **Performance Tuning**<br/>• Benchmark 100K records/sec<br/>• Memory-mapped segments<br/>• Async flush optimization |

#### Counter-Evidence Tests
```bash
# Sprint 1.1
scripts/counter_evidence/cee_smoke.py  # 5K commands, 95%+ success

# Sprint 1.2  
scripts/counter_evidence/trace_continuity.py  # Verify trace propagation
scripts/counter_evidence/actor_metrics.py  # Mailbox recording under load

# Sprint 1.3
scripts/counter_evidence/provenance_durability.py  # Kill writer, zero loss
```

---
## Phase 2: Security & Adaptive Systems (Weeks 4-6)

### Sprint 2.1 – Capability-Aware Routing v2 (Days 22-28)

#### Detailed Tasks
| Day | Tasks |
|-----|-------|
| 22-23 | **Dynamic Capability Discovery**<br/>• Runtime capability negotiation<br/>• Capability inheritance chains<br/>• Denial event enrichment |
| 24-25 | **Biscuit Token Integration**<br/>• Replace JSON with Biscuit v2<br/>• Caveat evaluation at routing<br/>• Token attenuation support |
| 26 | **Security Metrics**<br/>• Capability check latency<br/>• Denial reasons breakdown<br/>• Token validation errors |
| 27-28 | **Audit Trail**<br/>• All security decisions in WaveTrace<br/>• Capability usage patterns<br/>• Compliance report generation |

### Sprint 2.2 – ACF Auto-Tuning (Days 29-35)

#### Detailed Tasks
| Day | Tasks |
|-----|-------|
| 29-30 | **Reinforcement Learning Policy**<br/>• PPO-lite agent for fidelity<br/>• Reward based on latency/accuracy<br/>• Offline training pipeline |
| 31-32 | **Real-time Inference**<br/>• Sub-5ms policy queries<br/>• LRU cache for decisions<br/>• Fallback to heuristics |
| 33 | **A/B Testing Framework**<br/>• Policy comparison flags<br/>• Metric collection<br/>• Statistical significance |
| 34-35 | **Energy Optimization**<br/>• M3 Max specific tuning<br/>• Power usage tracking<br/>• Efficiency metrics |

### Sprint 2.3 – Effect Strategy Optimization (Days 36-42)

#### Implementation from CAW Analysis
| Day | Tasks |
|-----|-------|
| 36-37 | **Wave-Particle Message Routing**<br/>• Dual representation in all messages<br/>• Downstream choice based on context<br/>• Branch decision recording |
| 38-39 | **Choreographed Effect Coordination**<br/>• Effect algebras for compatibility<br/>• Distributed coordination without orchestrator<br/>• Effect ordering verification |
| 40-41 | **Context-Driven Adaptation**<br/>• UnifiedContext drives all ACF decisions<br/>• Priority-fidelity correlation graphs<br/>• Automatic threshold adjustment |
| 42 | **Validation & Metrics**<br/>• Production graphs showing adaptation<br/>• Effect coordination success rates<br/>• Context propagation efficiency |

---
## Phase 3: Advanced Integration (Weeks 7-9)

### Sprint 3.1 – Differential Dataflow Engine (Days 43-49)

#### Detailed Tasks
| Day | Tasks |
|-----|-------|
| 43-44 | **Rust FFI Wrapper**<br/>• Timely/DD integration<br/>• Python async facade<br/>• Health monitoring |
| 45-46 | **Stream Builder DSL**<br/>• Intuitive API design<br/>• Type-safe operations<br/>• Error handling |
| 47-48 | **Wave Topic Pipeline**<br/>• User sentiment example<br/>• Incremental updates<br/>• Sub-100ms latency |
| 49 | **Performance Validation**<br/>• 10K msg/s throughput<br/>• CPU scaling verification<br/>• Memory efficiency |

### Sprint 3.2 – Choreography Compiler (Days 50-56)

#### Detailed Tasks
| Day | Tasks |
|-----|-------|
| 50-51 | **DSL Grammar**<br/>• Lark parser for .cho files<br/>• Participant declarations<br/>• Message flow syntax |
| 52-53 | **Compiler Implementation**<br/>• Generate handler stubs<br/>• Auto-registration<br/>• Deadlock detection |
| 54-55 | **Choreography Provenance**<br/>• Stamp choreo_id header<br/>• Step tracking<br/>• Flow visualization |
| 56 | **End-to-end Testing**<br/>• UserRegistration example<br/>• Multi-participant flows<br/>• Failure scenarios |

### Sprint 3.3 – Observability Platform (Days 57-63)

#### Detailed Tasks
| Day | Tasks |
|-----|-------|
| 57-58 | **OpenTelemetry Bridge**<br/>• OTLP export format<br/>• Trace context mapping<br/>• Metric correlation |
| 59-60 | **Grafana Tempo Integration**<br/>• Trace visualization<br/>• Service dependency maps<br/>• Latency breakdown |
| 61-62 | **Advanced Dashboards**<br/>• CAW principle verification<br/>• Actor supervision health<br/>• Choreography flow times |
| 63 | **Alerting Rules**<br/>• Anomaly detection<br/>• SLO monitoring<br/>• Incident automation |

---
## Phase 4: Production Readiness (Weeks 10-12)

### Sprint 4.1 – Performance Optimization (Days 64-70)

#### Detailed Tasks
| Day | Tasks |
|-----|-------|
| 64-65 | **Kafka Client Migration**<br/>• confluent-kafka integration<br/>• 30% latency reduction target<br/>• Idempotent producers |
| 66-67 | **Queue Optimizations**<br/>• Lock-free structures where possible<br/>• NUMA-aware allocation<br/>• Cache line optimization |
| 68-69 | **Backpressure Implementation**<br/>• Adaptive queue sizing<br/>• Source throttling<br/>• Graceful degradation |
| 70 | **Benchmark Suite**<br/>• Reproducible scenarios<br/>• Performance regression detection<br/>• Hardware-specific profiles |

### Sprint 4.2 – Resilience & Recovery (Days 71-77)

#### Detailed Tasks
| Day | Tasks |
|-----|-------|
| 71-72 | **Circuit Breakers**<br/>• Channel-level breakers<br/>• Automatic recovery<br/>• Half-open testing |
| 73-74 | **Bulkhead Isolation**<br/>• Resource pool separation<br/>• Failure containment<br/>• Priority preservation |
| 75-76 | **Chaos Engineering**<br/>• Fault injection framework<br/>• Automated chaos tests<br/>• Recovery validation |
| 77 | **Disaster Recovery**<br/>• State reconstruction<br/>• Provenance replay<br/>• RTO/RPO targets |

### Sprint 4.3 – Documentation & Migration (Days 78-84)

#### Detailed Tasks
| Day | Tasks |
|-----|-------|
| 78-79 | **Migration Scripts**<br/>• Legacy bus removal<br/>• Import rewriting<br/>• Compatibility stubs |
| 80-81 | **Production Guides**<br/>• Deployment playbooks<br/>• Tuning guidelines<br/>• Troubleshooting trees |
| 82-83 | **API Documentation**<br/>• Complete docstrings<br/>• Usage examples<br/>• Best practices |
| 84 | **Final Validation**<br/>• All tests green<br/>• Feature matrix complete<br/>• Sign-off checklist |

---
## Success Metrics

### Performance
- **Throughput**: ≥20K msg/s sustained (2x improvement)
- **Latency P99**: ≤25ms for priority messages (50% reduction)
- **ACF Efficiency**: 30% resource reduction at 0.5 fidelity

### Reliability
- **Provenance Loss**: <0.1% under all conditions
- **Security Denials**: 100% logged with full context
- **Actor MTTR**: ≤1s (50% improvement)
- **Trace Completeness**: 100% messages traceable

### CAW Principles
- **Context Propagation**: <2x overhead for incremental updates
- **Wave-Particle Decisions**: 100% recorded in WaveTrace
- **Capability Routing**: Zero unauthorized deliveries
- **Effect Coordination**: 100% ordering preserved

---
## Risk Mitigation

| Risk | Mitigation | Contingency |
|------|------------|-------------|
| WaveTrace performance impact | Async writers, batching | Sampling mode |
| Trace context overhead | Efficient serialization | Compression |
| RL policy instability | Extensive offline testing | Heuristic fallback |
| Choreography complexity | Start with simple flows | Manual coordination |
| Migration disruption | Parallel operation period | Rollback plan |

---
## Deliverables by Phase

### Phase 1 Deliverables
- ✅ Complete CEE flow with registry
- ✅ WaveTrace with full context propagation
- ✅ Native provenance with trace indexing
- ✅ Actor metrics and CAW branch tracking

### Phase 2 Deliverables
- ✅ Biscuit capability tokens
- ✅ RL-based ACF policy
- ✅ Wave-particle routing
- ✅ Security audit trail

### Phase 3 Deliverables
- ✅ Differential dataflow engine
- ✅ Choreography compiler
- ✅ OpenTelemetry integration
- ✅ Production dashboards

### Phase 4 Deliverables
- ✅ 30% latency reduction
- ✅ Circuit breakers and bulkheads
- ✅ Complete documentation
- ✅ Zero legacy dependencies

---
## Next Steps

1. **Immediate** (This Week)
   - Enable WaveTrace in staging environment
   - Implement trace context propagation (Sprint 1.2)
   - Deploy enhanced provenance schema

2. **Short Term** (Next Month)
   - Complete Phase 1 sprints
   - Begin security enhancements
   - Start RL policy training

3. **Medium Term** (Quarter)
   - Full CAW principle verification in production
   - Choreography flows operational
   - Complete observability platform

4. **Long Term** (Next Quarter)
   - Quantum-ready provenance
   - Differential programming integration
   - Global deployment optimization

---
## Appendix: Implementation Commands

### Enable WaveTrace
```bash
export PS_ENABLE_WAVETRACE=1
export PS_PROVENANCE_SINK=native
export PS_WAVETRACE_DIR=/var/lib/person_suit/traces
```

### Query Traces
```bash
# Using DuckDB
duckdb -c "SELECT * FROM read_parquet('traces/*.parquet') WHERE trace_id = '...'"

# Using native reader
python -m person_suit.tools.read_wavetrace --trace-id ... --format json
```

### Monitor CAW Principles
```bash
# Real-time dashboard
curl -X POST http://localhost:3000/api/dashboards/db \
  -H "Content-Type: application/json" \
  -d @devops/monitoring/grafana/caw_principles_dashboard.json
```

### Validate Implementation
```bash
# Run all counter-evidence tests
make test-counter-evidence

# Specific phase validation
make test-phase-1
make test-phase-2
make test-phase-3
make test-phase-4
```