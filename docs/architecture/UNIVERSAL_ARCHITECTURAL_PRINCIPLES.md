# Universal Architectural Principles for the PersonSuit System (v3 - Quantum-Ready Edition)

## 1. Introduction: The Constitution of Our Codebase

This document codifies the five foundational architectural principles for the entire PersonSuit project. It is the constitution that governs all development. Adherence is not optional; it is the prerequisite for contributing to this codebase.

Our philosophy is to build a single, cohesive, and intelligent system, not a collection of disparate parts. We are architecting a **predictive, self-tuning, and context-aware ecosystem** that learns, adapts, and evolves. These principles are the laws that make that possible. They apply universally to every meta-system (`PersonaCore`, `Analyst`, `Predictor`), every core service, and every I/O component.

---

## Principle I: Absolute Decoupling through Choreographed Effects

### **Core Tenet: "Components shall not act. They shall only declare intent. The system choreographs the fulfillment of that intent."**

#### **Simple Analogy**
Think of a high-end restaurant kitchen. A chef (a "Component") doesn't run to the storeroom for ingredients or serve the food themselves. Instead, they create a series of tickets (declarative `Effects`): `Prep('Mirepoix')`, `Fire('Steak', 'Medium-Rare')`, `Plate('Dish', [Steak, Sauce])`. The kitchen staff (the `Effect Interpreter`) executes these tickets. The master recipe for the entire meal is the `Choreography`—a predefined protocol that guarantees the steak, sauce, and sides are all ready at the correct time without the chef needing to manage the staff directly. The chef's job is to describe the "what," not the "how."

#### **The "Why"**
This elevates decoupling beyond simple message passing. Components become pure, testable functions that map inputs to a *description* of side effects, not the execution of them. This makes business logic transparent, eliminates hidden dependencies, and enables a new level of control. The system can now intercept, audit, modify, and optimize effects before they ever happen. Choreographies ensure that complex, multi-component interactions are correct by construction, preventing deadlocks and race conditions.

#### **Implementation Steps**
1.  **Define Effects**: In `person_suit/core/effects/`, define your side effects as data classes (e.g., `ReadDatabase`, `PublishEvent`, `CallAPI`). These are inert data structures.
2.  **Declare Intent**: A component's method does not perform I/O. Instead, it returns an `Effect` or a `Composition` of multiple effects.
3.  **Use an Interpreter**: A central `EffectInterpreter` (part of the core infrastructure) is responsible for receiving these effect descriptions.
4.  **Execute Effects**: The interpreter is the *only* part of the system that actually performs I/O. Before executing, it checks the operation's `UnifiedContext` against the `CapabilityManager` (Principle III).
5.  **Define Choreographies**: For complex processes, define a `Choreography` that specifies the sequence and participants of an interaction. This allows for compile-time validation of distributed workflows.

#### **Gold-Standard Code Example**

**The WRONG Way (Implicit Side-Effects):**
```python
#- W R O N G ----------------------------------------------------
# This service is impure. It directly touches the database and the
# message bus, making it hard to test and reason about. Its
# responsibilities are mixed.
class UserService:
    def __init__(self, db, bus):
        self.db = db
        self.bus = bus

    async def register_user(self, username: str):
        # Implicitly performs two side effects.
        user = self.db.save(username)
        event = Event(topic="user.registered", payload={"user": user})
        await self.bus.publish(event)
        return user
#----------------------------------------------------------------
```

**The RIGHT Way (Choreographed Effects):**
```python
#+ R I G H T ++++++++++++++++++++++++++++++++++++++++++++++++++++
# person_suit/core/effects/user_effects.py (The "What")
@dataclass
class SaveUserToDatabase(Effect):
    username: str

# person_suit/shared/events/user_events.py (The Contract)
# ... UserRegisteredPayload definition ...

# person_suit/services/user_service.py (The Pure Component)
from ..core.effects.base import Effect, Composition
from ..core.effects.db_effects import WriteDatabase
from ..core.effects.event_effects import PublishEvent
from ..shared.events.base import Event
from ..shared.events.user_events import UserRegisteredPayload

class UserService:
    # No dependencies on I/O systems!
    def register_user(self, username: str, new_user_id: str) -> Effect:
        # Returns a *description* of the work to be done.
        # This method is now pure, predictable, and easy to unit test.
        payload = UserRegisteredPayload(username=username, user_id=new_user_id)
        
        return Composition(effects=[
            WriteDatabase(table="users", data={"id": new_user_id, "name": username}),
            PublishEvent(topic="user.registration.success", payload=payload)
        ])

# Somewhere in the core infrastructure (The "How")
# effect_interpreter.py
async def handle_request(request):
    # ...
    user_service = UserService()
    # 1. Get the declarative effect object.
    effect_description = user_service.register_user("Alice", "user-123")
    
    # 2. The interpreter runs the effect, checking capabilities.
    # This is the ONLY place side-effects happen.
    await interpreter.execute(effect_description, context)
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
```

#### **Architectural Litmus Test**
- Does my component's code contain a `.execute()`, `.publish()`, `.save()`, or `.call()` method? If yes, fail.
- Is my component's logic pure and testable without mocking any I/O services?
- Does my component return a description of work (`Effect`) rather than a result?

#### **Benefits**
- **Extreme Testability**: Components can be tested as pure functions.
- **Enhanced Security**: All actions are validated against capabilities at a single point (the interpreter).
- **Correct-by-Construction**: Choreographies mathematically prove the correctness of complex interactions.
- **Resilience & Debugging**: Effects can be logged, retried, or even modified before execution.

---

## Principle II: Contextual Supremacy

### **Core Tenet: "An event without context is noise. An action without context is reckless."**

#### **Simple Analogy**
Imagine a doctor in a hospital. A monitor beeps with a heart rate of 120 bpm. Is this an emergency? It's impossible to know without context.
- **Context A**: The patient is a marathon runner on a treadmill for a stress test. **Action**: Do nothing, this is normal.
- **Context B**: The patient is in a coma in the ICU. **Action**: Sound a critical alarm, this is a crisis.
The data (120 bpm) is the same, but the `UnifiedContext` (patient's state, location, priority) dictates the action.

#### **The "Why"**
Context allows our system to make intelligent, adaptive decisions. It prevents components from acting foolishly on raw data. It is the key to implementing Adaptive Computational Fidelity (ACF), where we allocate more resources to high-priority tasks and fewer to background jobs.

#### **Implementation Steps**
1.  **Require Context**: Any method that initiates an operation or handles an event MUST accept `context: UnifiedContext` as an argument.
2.  **Propagate Context**: The `EffectInterpreter` is responsible for stamping every `Effect` it receives with the context of the parent operation. Context must flow uninterrupted.
3.  **Interrogate Context**: When an `Effect` is about to be executed, the interpreter first queries the context. `if context.priority == Priority.REALTIME:` or `if context.security.sensitivity == 'high':`. The interpreter's logic, not the component's, must branch based on this information.

#### **Gold-Standard Code Example**
```python
from ..core.context.unified import UnifiedContext, Priority
from ..core.effects.llm_effects import ProcessWithLanguageModel

class TextAnalysisService:
    def analyze_text(self, text: str) -> Effect:
        # The component doesn't know about different models.
        # It just declares the INTENT to process text.
        return ProcessWithLanguageModel(text=text)

# in the Effect Interpreter...

class EffectInterpreter:
    async def execute_process_with_language_model(
        self, 
        effect: ProcessWithLanguageModel, 
        context: UnifiedContext
    ):
        # First, interrogate the context.
        if context.priority >= Priority.HIGH:
            # High priority context? Use the best, most expensive model.
            print("CONTEXT-AWARE: Interpreting effect with high-fidelity model.")
            return await large_language_model.process(effect.text)
        else:
            # Low priority? Use a cheaper, faster model. Conserve resources.
            print("CONTEXT-AWARE: Interpreting effect with low-fidelity model.")
            return await small_fast_model.process(effect.text)
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
```

#### **Architectural Litmus Test**
- Is my system re-calculating large states from scratch in response to small events? If so, fail.
- Does my system have components that explicitly maintain continuous, incrementally updated views of data?
- Are there separate topics for raw "particle" streams and aggregated "wave" updates?

#### **Benefits**
- **Performance**: Drastically reduces computational load for real-time analysis.
- **Predictive Power**: Enables the system to see and react to developing trends instantly.
- **Simplicity**: Consuming components can react to the clean "wave" signal instead of processing the noisy "particle" stream.

---

## Principle III: Capability as Sole Authority

### **Core Tenet: "Possession of a capability is the only valid form of authority. All else is assumption."**

#### **Simple Analogy**
Think of a valet parking key. When you give the valet your key, you're not giving them the master key to the car that lets them open the trunk and glove box. You're giving them a special key (a `Capability`) that only allows them to `vehicle:drive` and `vehicle:lock`. They cannot access the trunk because they do not possess the `vehicle:trunk:access` capability. Their role as "valet" doesn't matter; what matters is the specific, limited authority granted by the key they hold.

#### **The "Why"**
This creates a fine-grained, Zero-Trust security model that is vastly superior to simple role-based checks. It prevents "confused deputy" attacks, where a component with broad authority is tricked into misusing it. By coupling capabilities with the Effect System, security becomes a property of the interpreter, not a concern scattered across the business logic.

#### **Implementation Steps**
1.  **Define Capabilities**: In a central, shared location, define your capabilities as strings: `domain:resource:action` (e.g., `database:users:delete`).
2.  **Associate with Effects**: Each `Effect` class should declare the capability required to execute it. `class DeleteUser(Effect): required_capability = "database:users:delete"`.
3.  **Grant at Entry**: When a request enters the system, the entry-point service creates the `UnifiedContext` and populates it with the list of capabilities granted for that specific session.
4.  **Enforce at Interpretation**: In the `EffectInterpreter`, before executing *any* effect, perform one check: `capability_manager.has(context, effect.required_capability)`. This single check secures the entire system.

#### **Gold-Standard Code Example**

**The WRONG Way (Scattered, Role-Based Checks):**
```python
#- W R O N G ----------------------------------------------------
# Security logic is mixed with business logic and scattered
# across many services. Hard to audit, easy to get wrong.
class UserService:
    async def delete_user(self, user_id: str, performing_user: User):
        if performing_user.role == "admin":
             #... delete user from database ...
        else:
            raise PermissionError("Only admins can delete users.")
#----------------------------------------------------------------
```

**The RIGHT Way (Capability-Driven Effect Interpretation):**
```python
#+ R I G H T ++++++++++++++++++++++++++++++++++++++++++++++++++++
from ..core.effects.base import Effect
from ..core.capabilities.manager import CapabilityManager
from ..core.context.unified import UnifiedContext
from ..core.errors import CapabilityError

# 1. Define the Effect and its required capability.
@dataclass
class DeleteUserFromDatabase(Effect):
    user_id: str
    # The capability is inseparable from the action itself.
    required_capability: str = "database:users:delete"

# 2. The business logic is clean and security-agnostic.
class UserService:
    def request_user_deletion(self, user_id: str) -> Effect:
        return DeleteUserFromDatabase(user_id=user_id)

# 3. The interpreter enforces security universally.
class EffectInterpreter:
    def __init__(self, capability_manager: CapabilityManager):
        self.capability_manager = capability_manager

    async def execute(self, effect: Effect, context: UnifiedContext):
        # THE ONE AND ONLY CHECK
        if self.capability_manager.has(context, effect.required_capability):
            print(f"CAPABILITY-CHECK PASSED: Executing {effect.__class__.__name__}")
            # ... dispatch to the actual I/O logic ...
        else:
            raise CapabilityError(f"Missing capability: {effect.required_capability}")
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
```

#### **Architectural Litmus Test**
- Is there any security logic (checking roles or permissions) inside a regular business component? If yes, fail.
- Does every `Effect` that performs a sensitive operation have a `required_capability` attribute?
- Is the capability check performed universally in the interpreter, or is it left up to individual effect handlers? It must be universal.

#### **Benefits**
- **Auditability**: To see who can delete users, you just find who is granted `"database:users:delete"`. The logic is in one place.

---

## Principle IV: Differentiable by Design

### **Core Tenet: "A static component is a predictable and un-adaptive component. All parameters should be learnable."**

#### **Simple Analogy**
Imagine tuning a guitar. You can use a static pitch pipe (a "magic number" in a config file) to get close to the right note. Or, you can use an electronic tuner (a "differentiable component"). The tuner listens to the note you play (input), compares it to the target note (the truth), shows you the error (the "loss"), and tells you whether to tune up or down (the "gradient"). By following its feedback, you perfectly tune the guitar.

#### **The "Why"**
This principle creates a system that can self-optimize and adapt to changing data and environments. It replaces brittle, hand-tuned "magic numbers" with intelligent parameters that improve automatically. By integrating with the Effect System, we can even learn optimal policies for *interpreting* effects (e.g., learning the best retry strategy for a failing API call based on context).

#### **Implementation Steps**
1.  **Identify Parameters**: Find any static threshold, weight, or policy in your code (e.g., `CONFIDENCE_THRESHOLD = 0.95`, `RETRY_COUNT = 3`).
2.  **Make them Differentiable**: Use a framework like PyTorch or JAX. Define the parameter as a learnable tensor. `self.threshold = torch.nn.Parameter(torch.tensor(0.95))`. These parameters can live within components or even within the `EffectInterpreter` itself.
3.  **Establish a Feedback Loop**: An evaluation component must be ableto publish a feedback `Event` (e.g., `ClassificationIncorrectEvent`, `EffectExecutionFailedEvent`).
4.  **Implement the Learning Step**:
    *   The component (or interpreter) subscribes to its own feedback events.
    *   When feedback is received, it calculates a "loss" (a number representing how wrong it was).
    *   It uses this loss to calculate a "gradient" (the direction to adjust the parameter).
    *   It updates the parameter. An optimizer like `torch.optim.Adam` makes this easy: `loss.backward(); optimizer.step(); optimizer.zero_grad()`.

#### **Gold-Standard Code Example**

**The WRONG Way (Static and Brittle):**
```python
#- W R O N G ----------------------------------------------------
# This threshold was chosen by a developer. It might work well
# today, but be totally wrong next week when the data changes.
# It is brittle and requires manual re-tuning.
SPAM_CONFIDENCE_THRESHOLD = 0.98

class SpamFilter:
    def is_spam(self, email_features) -> bool:
        confidence = model.predict(email_features)
        return confidence > SPAM_CONFIDENCE_THRESHOLD
#----------------------------------------------------------------
```

**The RIGHT Way (Differentiable and Adaptive):**
```python
#+ R I G H T ++++++++++++++++++++++++++++++++++++++++++++++++++++
import torch
import torch.nn as nn
from torch.optim import Adam

# A service that can learn from its mistakes.
class DifferentiableSpamFilter:
    def __init__(self, bus: HybridMessageBus):
        # 1. The threshold is now a learnable parameter.
        self.threshold = nn.Parameter(torch.tensor(0.98))
        self.optimizer = Adam([self.threshold], lr=0.01)
        
        # 2. Subscribe to feedback about its performance.
        bus.subscribe("spam.feedback.incorrect_ham", self.on_incorrect_ham)
        
    def classify_spam(self, confidence: torch.Tensor) -> Effect:
        # The component doesn't decide if it's spam.
        # It returns an effect with the model's confidence.
        # The decision logic lives elsewhere, maybe even in the interpreter.
        return ClassifyAsSpam(confidence=confidence, threshold=self.threshold)

    def on_incorrect_ham(self, event: Event):
        # This handler is our feedback loop / learning step.
        # We were told we marked a good email (ham) as spam.
        # We need to LOWER our threshold to be less aggressive.
        print(f"LEARNING: Received feedback. Old threshold: {self.threshold.item()}")
        
        # 3. Calculate loss: how far was our threshold from the confidence?
        # We want the threshold to be lower than the confidence of the missed email.
        confidence_of_missed_email = torch.tensor(event.payload.confidence)
        loss = (self.threshold - confidence_of_missed_email)**2
        
        # 4. Update the parameter using the magic of autodiff.
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
        
        print(f"LEARNING: Parameter updated. New threshold: {self.threshold.item()}")
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
```

#### **Architectural Litmus Test**
- Does my component have hard-coded numbers that determine its behavior? If yes, ask "Could feedback improve this number?" If the answer is yes, fail.
- Is my component's performance static over time, or does it have a mechanism to improve based on results? If static, fail.

#### **Benefits**
- **Adaptability**: The system automatically adjusts to new patterns in data.
- **Self-Optimization**: Finds better operating parameters than humans could.
- **Reduced Maintenance**: Less time spent manually re-tuning configuration files.

---

## Principle V: CAW Duality via Differential Dataflow

### **Core Tenet: "Treat discrete 'particles' of data as incremental updates to a continuous 'wave' of understanding."**

#### **Simple Analogy**
Imagine monitoring a river for flood risk. A single sensor reporting a 1cm rise in water level is a "particle"—a discrete event. It's not a flood. A "wave" is the continuously updated *model* of the entire river system. When the particle arrives, you don't re-measure the whole river. You use **Differential Dataflow** to incrementally update your model, calculating only what has changed. This allows you to see the wave pattern—the overall water level rising across multiple sensors—and predict a flood with minimal computational effort.

#### **The "Why"**
This provides a concrete, high-performance implementation for the CAW Duality concept. It allows the system to maintain complex, always-up-to-date views of the world (the "wave") by efficiently processing streams of discrete events (the "particles"). This is essential for moving from a reactive to a predictive system, enabling sophisticated pattern detection, trend analysis, and hypothesis generation in near real-time.

#### **Implementation Steps**
1.  **Define Dataflows**: Identify a "wave" you want to maintain (e.g., a user's current emotional state, the system's security posture, a trending topic).
2.  **Use a Differential Dataflow Engine**: Implement this using a suitable framework (like Timely Dataflow/Differential Dataflow, or a Python equivalent). The dataflow subscribes to streams of "particle" events.
3.  **Process Incrementally**: The engine receives particle events (e.g., `UserActionHappened`, `LoginFailed`) and uses them to incrementally update its internal state. The key is that the computational cost is proportional to the size of the *change*, not the size of the total state.
4.  **Publish the Wave**: The dataflow's output is the "wave"—a continuously updated stream of changes to the aggregated view (e.g., `UserEmotionalStateChanged`, `ThreatLevelUpdated`). Other components can subscribe to this wave to get a high-level understanding without processing the raw particles themselves.

#### **Gold-Standard Code Example**

**The WRONG Way (Brute-Force, Batch Processing):**
```python
#- W R O N G ----------------------------------------------------
# This handler re-calculates the user's entire history on every
# single action. It's incredibly inefficient and will not scale.
# It is "wave-blind."
class UserSentimentTracker:
    def on_user_action(self, event: Event):
        all_actions = db.get_all_actions_for_user(event.user_id)
        current_sentiment = self.recalculate_sentiment(all_actions)
        # ...
#----------------------------------------------------------------
```

**The RIGHT Way (Differential Dataflow):**
```python
#+ R I G H T ++++++++++++++++++++++++++++++++++++++++++++++++++++
# This is a conceptual example of a Differential Dataflow setup.

# 1. The "Wave" Processor (A Differential Dataflow Graph)
class UserSentimentDataflow:
    def __init__(self, bus: HybridMessageBus):
        self.bus = bus
        # Input stream for raw user action "particles"
        self.action_stream = Stream() 
        
        # Define the dataflow graph. This is a declarative definition
        # of the "wave" computation.
        self.sentiment_wave = self.action_stream \
            .map(lambda action: (action.user_id, action.sentiment_score)) \
            .group_by_key() \
            .average() # Continuously updated average sentiment per user

        # Subscribe the dataflow to the bus to receive particles.
        bus.subscribe("user.action.happened", self.on_user_action)
        
        # Whenever the wave changes, publish the update.
        self.sentiment_wave.on_change(self.publish_wave_update)

    def on_user_action(self, event: Event):
        # Feed the new "particle" into the dataflow. The engine handles
        # the efficient, incremental update automatically.
        self.action_stream.insert(event.payload)

    def publish_wave_update(self, user_id, new_average_sentiment):
        # Publish the change in the wave, not the raw data.
        wave_event = Event(
            topic="user.sentiment.wave_updated",
            payload={"user_id": user_id, "average_sentiment": new_average_sentiment}
        )
        await self.bus.publish(wave_event)

# 2. A "Particle" consumer that uses the wave.
class AlertManager:
    def on_sentiment_wave_updated(self, event: Event):
        # This component reacts to the high-level understanding.
        if event.payload.average_sentiment < -0.8:
            print("ALERT: User sentiment has become extremely negative!")
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
```

#### **Architectural Litmus Test**
- Is my system re-calculating large states from scratch in response to small events? If so, fail.
- Does my system have components that explicitly maintain continuous, incrementally updated views of data?
- Are there separate topics for raw "particle" streams and aggregated "wave" updates?

#### **Benefits**
- **Performance**: Drastically reduces computational load for real-time analysis.
- **Predictive Power**: Enables the system to see and react to developing trends instantly.
- **Simplicity**: Consuming components can react to the clean "wave" signal instead of processing the noisy "particle" stream.

## Principle VI: Provenance & Observability

### **Core Tenet: "If we cannot _see_ it, we cannot _trust_ it."**

#### **Simple Analogy**
Think of an aircraft's black-box flight recorder. It captures every sensor reading and pilot decision, allowing investigators to _reconstruct_ the flight second-by-second.  In the same way, the PersonSuit system must continuously record the causality chain that led to every observable behaviour.  Only with a reliable, queryable provenance trail can we prove correctness, diagnose anomalies, and demonstrate compliance.

#### **The "Why"**
1. **Security & Compliance** – Capability checks are only useful if their outcomes are immutably recorded and tamper-evident.
2. **ACF Feedback** – Adaptive fidelity requires high-resolution telemetry on resource usage and quality metrics to learn optimal policies.
3. **Emergent Behaviour Validation (CAW Emergence Rule)** – Emergent properties cannot be hand-waved; they must be _measured_ in production.

#### **Implementation Steps**
1. **Event Sourcing Core** – Every `HybridMessage` that enters or exits the bus is persisted to an append-only log (e.g., Apache Pulsar, Redpanda, or FoundationDB streams) with nanosecond timestamps.
2. **Deterministic IDs** – Use content-addressable, SHA-256-based IDs for messages and effects (`message_id = sha256(serialised_payload)`), ensuring idempotency and integrity.
3. **eBPF-Based Tracepoints** – Attach eBPF probes to critical kernel pathways (network, syscalls) so that even non-Python components (e.g., WASM handlers, Zig micro-services) are included in end-to-end traces with <1 µs overhead.
4. **Provenance Graph** – Continuously stream the log into a temporal property-graph (e.g., Neo4j + Timescale or TigerGraph) enabling queries like: _"Show all Effects caused by `correlation_id = X` that touched capability `database:users:delete"._
5. **Real-Time Observability Dashboards** – Provide Grafana/Chronograf boards fed by the provenance graph & metrics pipeline.  Alerts must be contextualised by UnifiedContext fields so that operators see _why_ an alert is important, not just _that_ it occurred.

#### **Architectural Litmus Test**
- Can we reconstruct, from immutable logs, the exact chain of Messages → Effects → Events that produced any external side-effect?  If not, fail.
- Do dashboards expose live ACF curves against system load?  If not, fail.
- Are eBPF/USDT tracepoints or equivalent kernel hooks enabled in production?  If not, fail (except on constrained targets where a specialised interpreter provides alternate provenance guarantees).

#### **Benefits**
- **Forensics-Grade Auditing** – Every state transition is provably accounted for.
- **Self-Tuning** – Fine-grained telemetry closes the feedback loop for Differentiable & ACF mechanisms.
- **Trust** – Clear, cryptographically verifiable provenance underpins security claims and eases external certification.

## 3. Architectural Integrity Checklist (updated)

Before committing any code to any part of the system, verify it against these principles. A "no" answer indicates an architectural violation that must be corrected.

1.  **Decoupling & Effects**: Does this change introduce any direct I/O into a business logic component? Does the component return a declarative `Effect` instead? (Y/N)
2.  **Context**: Is every operation wrapped in a `UnifiedContext` that is passed to and respected by the `EffectInterpreter`? (Y/N)
3.  **Capabilities**: Is every `Effect` associated with a specific capability that is checked *only* by the interpreter? (Y/N)
4.  **Differentiability**: Are new parameters or thresholds designed to be learnable, or are they static and brittle? (Learnable/Static)
5.  **Duality & Dataflow**: Does this component correctly process streams of data? If it performs aggregation, does it do so incrementally (like a dataflow) instead of via brute-force recalculation? (Y/N)
6.  **Provenance & Observability**: Does the change preserve the end-to-end provenance trail (event-sourced log, tracepoints, dashboards)?  Are new metrics emitted with UnifiedContext correlation? (Y/N) 