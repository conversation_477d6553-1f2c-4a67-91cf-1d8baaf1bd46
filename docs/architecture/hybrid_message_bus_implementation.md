# Hybrid Message Bus Implementation Plan (v2 - Effect-Aware)

## Executive Summary

This document outlines the implementation and validation plan for Person Suit's hybrid message bus architecture. This updated plan aligns with the "v3" universal architectural principles, evolving from a simple pub/sub system to an **effect-aware, choreography-ready backbone** for the entire PersonSuit ecosystem. It combines channel-based routing with rich ACF metadata and a clear pattern for handling commands, effects, and events.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Implementation Plan](#implementation-plan)
3. [Channel Structure](#channel-structure)
4. [Message Types and Flows](#message-types-and-flows)
5. [ACF Integration](#acf-integration)
6. [Migration Strategy](#migration-strategy)
7. [Validation Plan](#validation-plan)
8. [Performance Considerations](#performance-considerations)
9. [Example Scenarios](#example-scenarios)
10. [Provenance & Observability](#provenance-observability)
11. [Implementation Optimisation & Hardening Roadmap](#implementation-optimisation-hardening-roadmap)

## Architecture Overview

### Core Principles

1. **Single Message Bus**: One unified bus for all inter-component communication
2. **Channel-Based Routing**: Fast, topic-based message routing using hierarchical channels
3. **Command/Effect/Event Separation**: Clear distinction between telling a component to do something (Command), describing a side-effect (Effect), and announcing that something has happened (Event)
4. **Interpreter-Centric Execution**: Side-effects are not executed by components, but by a central, capability-aware `EffectInterpreter`
5. **Choreography-Ready**: The architecture is a stepping stone towards formally defined choreographies, moving beyond simple pub/sub
6. **CAW Alignment**: Wave-particle duality and context propagation are embedded in every message

### Key Components & Message Flow

The core interaction loop is now **Command -> Handler -> Effect -> Interpreter -> Event**

1. A component sends a **Command** message (e.g., `RequestUserDeletion`)
2. A stateless **Handler** receives the Command and returns an **Effect** message (e.g., `DeleteUserFromDatabase`). The handler contains only pure business logic
3. The **Effect** is routed to the central **EffectInterpreter**
4. The Interpreter validates the Effect against the message's context and capabilities, executes the I/O, and upon success, publishes a final **Event** message (e.g., `UserWasDeleted`)

```mermaid
graph TD
    A[Component] --sends--> B(Bus: COMMAND);
    B --routes to--> C{Handler};
    C --returns--> D(Bus: EFFECT);
    D --routes to--> E[Effect Interpreter];
    E --performs I/O & sends--> F(Bus: EVENT);
    F --routes to--> G((Subscribers));
```

```python
# Core message structure
from enum import Enum
import hashlib

class MessageType(Enum):
    COMMAND = "COMMAND" # Imperative: "Do this."
    EVENT = "EVENT"     # Past-tense: "This happened."
    EFFECT = "EFFECT"   # Declarative: "This action should be performed."
    QUERY = "QUERY"     # Interrogative: "What is the value of this?"

    @staticmethod
    def deterministic_id(payload: Dict[str, Any], ts_nanos: int) -> str:
        return hashlib.sha256(f"{payload}|{ts_nanos}".encode()).hexdigest()

@dataclass
class HybridMessage:
    # Identity
    message_id: str
    timestamp: float
    correlation_id: str # Links messages in a single workflow
    
    # Routing & Type
    message_type: MessageType
    channel: str  # e.g., "command.pc.memory.encode"
    source_channel: str  # sender's channel
    
    # Content
    payload: Dict[str, Any] # Can be a serialized Command, Event, or Effect
    
    # ACF Control
    acf_metadata: ACFMetadata
    
    # CAW Properties
    wave_particle_ratio: float
    context_propagation: Dict[str, Any]
    
    # Execution Control
    routing_hints: RoutingHints
    execution_constraints: ExecutionConstraints

    # Provenance flag – if True, the message is persisted to the
    # append-only provenance log (default).  Ultra-constrained
    # interpreters may set this to False and provide an alternate
    # summarisation strategy.
    record_provenance: bool = True
    
    # WaveTrace Integration (v2.1)
    trace_id: Optional[str] = None
    span_id: Optional[str] = None
    parent_span_id: Optional[str] = None
    trace_flags: int = 0
    trace_state: Dict[str, str] = field(default_factory=dict)
```

## Trinity Alignment & Current Status (2025-06-22)

### Mapping to the Why → What → How *Trinity*

| Trinity Layer | Hybrid Message Bus Contribution |
|---------------|---------------------------------|
| **Why** – *Design Philosophy* | Enables universal-deployment scalability and emergent behaviour by replacing direct imports with contextual, message-based interaction (see `DESIGN_PHILOSOPHY.md`). |
| **What** – *Universal Architectural Principles* | Materialises the principles of Contextual Adaptive Wave Programming (CAW) §2 through §8: dual state propagation, capability-based security, adaptive computational fidelity, provenance, graceful degradation, and choreography-readiness. |
| **How** – *Implementation Guide* | Realised as a slim `BusKernel` + pluggable middleware stack (`security`, `provenance`, `telemetry`, `acf`, `choreography`, `message_processing`).  Legacy monolith `hybrid_message_bus_legacy.py` is now **read-only** and scheduled for deletion once green tests confirm parity. |

### Feature Matrix (✅ done | 🟡 partial | ❌ missing)

| Capability | Status | Notes |
|------------|--------|-------|
| BusKernel priority queue + dispatch loop | ✅ | Covered by `PriorityMessageQueue` and `_dispatch()`.
| UnifiedContext auto-injection | ✅ | Implemented in `_prepare()`.
| DualInformation auto-wrap | ✅ | Implemented in `_prepare_message()`.
| ChannelRegistry defaults (priority / QoS / ACF) | ✅ | `_prepare_message()` merges defaults.
| Capability-aware message authorization | ✅ | Full checks in `middleware/security.py` with adaptive security manager integration.
| Capability-aware *routing* | ✅ | Implemented via `ChannelSubscription.capability_token` & filtering in `BusKernel._dispatch()`.
| Adaptive Computational Fidelity loop | ✅ | Closed-loop merges live queue depth & cached metrics; fidelity adapted per message pre-handler.
| EffectInterpreter & Command→Effect→Event flow | 🟡 | Interpreter scaffold exists; needs integration tests and ACF hooks.
| Differential Context Propagation | ✅ | BusKernel emits `sys.context.delta` events with minimal diffs; actor replay implemented.
| Choreography compiler / register API | 🟡 | `register_choreography()` helper exists; compiler still TODO.
| Persistent provenance backend | ✅ | Native WaveTrace backend operational with segment storage.
| House-keeping & metrics emission | ✅ | `_housekeeping()` emits `sys.metrics.bus` events.
| Legacy bus removal | 🟡 | All internal helpers ported; final deletion blocked by failing legacy tests.
| AdaptiveMessageRouter cleanup | ✅ | Obsolete class removed; routing now handled by BusKernel + ChannelRouter.
| WaveTrace span context propagation | ❌ | Messages lack trace_id/span_id fields (Sprint 1.2).
| Actor mailbox depth tracking | ❌ | Not recorded in WaveTrace (Sprint 1.2).
| CAW branch decision recording | ❌ | Wave vs particle paths not tracked (Sprint 1.2).
| Choreography flow markers | ❌ | No choreo_id stamping yet (Sprint 3.2).
| Security audit in traces | ❌ | Capability decisions not in WaveTrace (Sprint 2.1).

> 💡 The **Differential Context Propagation Rule** and **Choreographed Effect Rule** are now **satisfied** in production.

## Implementation Plan

### Phase 1: Core Infrastructure (Week 1-2)

#### 1.1 Fix Circular Dependencies
- ✅ Legacy `message_based_core.py` replaced by a deprecation stub that re-exports
  `_message_bridge` and emits a warning.  All production imports now use
  `HybridMessageBus` or `_message_bridge`, breaking the circular-import chain.
  (Full deletion scheduled once remaining meta-system modules are rewired.)
- ✅ Tracing dependencies eliminated
- ✅ Bootstrap validated and operational

#### 1.2 Implement Enhanced Message Structure
```python
# File: person_suit/core/infrastructure/hybrid_message.py

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from enum import Enum
import time
import uuid

class ChannelType(Enum):
    """Predefined channel types for type safety"""
    # Persona Core
    PC_MEMORY = "pc.memory"
    PC_EMOTION = "pc.emotion"
    PC_FOLDED_MIND = "pc.folded_mind"
    PC_COGNITION = "pc.cognition"
    
    # Analyst
    AN_PATTERN = "an.pattern"
    AN_ENTITY = "an.entity"
    AN_CONTEXT = "an.context"
    AN_TOKEN = "an.token"
    
    # Predictor
    PR_MODEL = "pr.model"
    PR_HYPOTHESIS = "pr.hypothesis"
    PR_TREND = "pr.trend"
    PR_NEURAL = "pr.neural"
    
    # System
    SYS_HEALTH = "sys.health"
    SYS_COORDINATE = "sys.coordinate"
    SYS_ACF = "sys.acf"
    SYS_MONITOR = "sys.monitor"

@dataclass
class ACFMetadata:
    """Adaptive Computational Fidelity metadata"""
    fidelity: int = SCALE  # Current fidelity level, integer bucket
    min_fidelity: int = int(0.3 * SCALE)  # Minimum acceptable
    max_fidelity: int = SCALE  # Maximum allowed
    
    # Resource hints
    cpu_intensive: bool = False
    memory_intensive: bool = False
    io_intensive: bool = False
    
    # Adaptation strategy
    can_degrade: bool = True
    prefers_cache: bool = False
    timeout_flexible: bool = True
    
    def get_execution_policy(self, system_load_int: int) -> 'ExecutionPolicy':
        """Determines execution strategy based on load and metadata."""
        # This logic can now be much richer
        policy = ExecutionPolicy()
        if system_load_int > int(0.8 * SCALE) and self.can_degrade:
            # Example: degrade by 30%
            policy.fidelity = max(self.min_fidelity, (self.fidelity * 7) // 10)
        
        if self.prefers_cache and system_load_int > int(0.6 * SCALE):
            policy.use_cache = True

        return policy

@dataclass
class ExecutionPolicy:
    """A concrete plan for how to execute an effect."""
    fidelity: int = SCALE
    use_cache: bool = False
    retry_attempts: int = 3
    timeout_ms: int = 5000

@dataclass
class RoutingHints:
    """Hints for intelligent routing"""
    load_sensitive: bool = True
    alternative_channels: List[str] = field(default_factory=list)
    preferred_handler: Optional[str] = None
    affinity_key: Optional[str] = None  # For sticky routing
    broadcast: bool = False
    
@dataclass
class ExecutionConstraints:
    """Constraints on message execution"""
    max_latency_ms: Optional[float] = None
    requires_ordering: bool = False
    idempotent: bool = True
    ttl_seconds: float = 300
    max_retries: int = 3
    retry_strategy: str = "exponential"
```

#### 1.3 Implement Effect Interpreter
```python
# File: person_suit/core/infrastructure/effect_interpreter.py
class EffectInterpreter:
    def __init__(self, capability_manager, bus):
        self.capability_manager = capability_manager
        self.bus = bus
        # This subscribes the interpreter to ALL effect messages
        bus.subscribe("effect.#", self.handle_effect)

    async def handle_effect(self, message: HybridMessage):
        if message.message_type != MessageType.EFFECT:
            return # Or log an error
        
        effect = deserialize(message.payload) # e.g., into a WriteDatabase object
        context = message.context_propagation # Simplified for example
        
        # Centralized capability check (Principle III)
        if not self.capability_manager.has(context, effect.required_capability):
            # Publish a failure event
            raise CapabilityError(...)

        # Centralized ACF logic (Principle II)
        policy = message.acf_metadata.get_execution_policy(get_system_load_int())
        
        # Dispatch to the specific handler that performs I/O
        result = await self.dispatch(effect, policy)

        # On success, publish the resulting event
        if result.success and result.event_payload:
            result_event = HybridMessage(
                message_type=MessageType.EVENT,
                channel=result.event_channel,
                payload=result.event_payload,
                # ... other fields ...
            )
            await self.bus.publish(result_event)
```

### Phase 2: Channel System (Week 2-3)

#### 2.1 Channel Registry Implementation
```python
# File: person_suit/core/infrastructure/channel_registry.py

class ChannelRegistry:
    """Registry for all message channels with ACF defaults"""
    
    def __init__(self):
        self.channels = {}
        self._initialize_default_channels()
    
    def _initialize_default_channels(self):
        """Set up all Person Suit channels with ACF defaults"""
        
        # Persona Core Channels
        self.register_channel("pc.memory.encode", {
            "description": "Memory encoding operations",
            "acf_defaults": {"fidelity": 0.8, "min_fidelity": 0.5},
            "handlers": ["MemoryEncoderService"],
            "qos": "guaranteed"
        })
        
        self.register_channel("pc.memory.retrieve", {
            "description": "Memory retrieval operations",
            "acf_defaults": {"fidelity": 0.9, "min_fidelity": 0.6},
            "handlers": ["MemoryRetrieverService"],
            "qos": "guaranteed"
        })
        
        self.register_channel("pc.emotion.process", {
            "description": "Emotion processing and analysis",
            "acf_defaults": {"fidelity": 0.7, "min_fidelity": 0.4},
            "handlers": ["EmotionProcessorService"],
            "qos": "best_effort"
        })
        
        # Analyst Channels
        self.register_channel("an.pattern.detect", {
            "description": "Pattern detection in data",
            "acf_defaults": {"fidelity": 0.6, "min_fidelity": 0.3},
            "handlers": ["PatternDetectorService"],
            "qos": "best_effort"
        })
        
        # ... more channels ...
```

### Phase 3: Migration (Week 3-4)

#### 3.1 Service Migration Order
1. **Core Services First**
   - Memory Service → MessageBasedMemoryService
   - Context Service → MessageBasedContextService
   - Security Service → MessageBasedSecurityService

2. **Meta-Systems**
   - Persona Core components
   - Analyst components  
   - Predictor components

3. **Shared Services**
   - I/O Layer
   - Monitoring
   - Configuration

## Channel Structure

### Hierarchical Naming Convention
The channel hierarchy now reflects message types.

```
<message_type>.<meta_system>.<subsystem>.<operation>

Examples:
- command.pc.memory.encode
- event.pc.memory.encoded
- effect.db.write
- query.an.pattern.status
- event.sys.health.checked
```

### Channel Properties
```python
channel_properties = {
    "pc.memory.encode": {
        "qos": "guaranteed",      # guaranteed | best_effort
        "priority": "high",       # critical | high | normal | low
        "ordering": "required",   # required | optional
        "ttl": 300,              # seconds
        "acf_defaults": {
            "fidelity": 0.8,
            "min_fidelity": 0.5,
            "cpu_intensive": True,
            "memory_intensive": True
        }
    }
}
```

## Message Types and Flows

### Example: Memory Encoding Flow (Effect-Aware)

This flow demonstrates the Command -> Effect -> Event pattern.

```python
# 1. Component sends a command to encode memory.
# It doesn't know how memory is stored.
encode_command = HybridMessage(
    message_type=MessageType.COMMAND,
    channel="command.pc.memory.encode",
    payload={
        "content": "User said hello",
        "importance": 0.8
    },
    # ... context, acf, etc. ...
)
# SENT TO BUS

# 2. A pure business logic handler receives the command.
# It returns a description of the effects to perform.
# File: person_suit/handlers/memory_handlers.py
@handles("command.pc.memory.encode")
def handle_encode_command(command: HybridMessage) -> HybridMessage:
    # Logic to decide what "encoding" means.
    db_effect = WriteDatabaseEffect(table="memories", data=...)
    pattern_effect = TriggerPatternAnalysisEffect(content=...)
    
    # Return a composition of effects
    return HybridMessage(
        message_type=MessageType.EFFECT,
        channel="effect.system.compose", # Route to interpreter
        payload=CompositionEffect([db_effect, pattern_effect]),
        # ... carry over context, correlation_id ...
    )

# 3. The EffectInterpreter receives and executes the effects.
# - It checks capabilities for database:write.
# - It executes the database write.
# - It executes the pattern analysis trigger.

# 4. After the WriteDatabaseEffect is successfully executed,
# the interpreter publishes the final event.
memory_stored_event = HybridMessage(
    message_type=MessageType.EVENT,
    channel="event.pc.memory.encoded",
    payload={
        "memory_id": "mem_123",
        "content": "User said hello"
    },
    # ...
)
# PUBLISHED BY INTERPRETER
```

## ACF Integration

### Dynamic Fidelity via the Effect Interpreter

With the new architecture, ACF becomes much more powerful. Instead of components degrading their own logic, the `EffectInterpreter` makes adaptive decisions about *how* to fulfill a requested `Effect`.

- An `Effect` like `QueryDatabase` is just a description.
- The `EffectInterpreter`, aware of system load via the `sys.metrics.update` channel, can choose how to execute it:
  - **Low Load**: Route to the primary, high-consistency database replica.
  - **High Load**: Route to a read-only, eventually-consistent cache.
  - **Critical Load**: Return a stale result immediately, if the effect's ACF metadata allows it.

This logic is centralized in the interpreter, making it consistent, auditable, and keeping business logic components completely pure.

```mermaid
graph TD
    subgraph SystemMonitorActor
        A[psutil] --> B{Metrics};
        B --> C[Event: 'sys.metrics.update'];
        C --> D(Bus);
    end

    subgraph EffectInterpreter
        D --subscribes--> E{Internal State for ACF (load_factor_int)};
        F[Incoming Effect] & E --> G{ACF Decision Logic};
        G --Low Load (e.g. < 600_000)--> H[Route to Primary DB];
        G --High Load (e.g. > 600_000)--> I[Route to Read Replica];
    end
```

## Actor System Integration

### Bus-Actor Architecture

The HybridMessageBus operates as the **central nervous system** with supervised actors providing **business logic containers**. This complementary architecture achieves:

- **Bus**: Infrastructure for routing, priority, security, and adaptation
- **Actors**: Business logic with supervision and state management
- **Bridge**: Seamless integration between the two worlds

```mermaid
graph TB
    subgraph "HybridMessageBus (Infrastructure)"
        HMB[Message Router<br/>- Channel routing<br/>- Priority queuing<br/>- ACF adaptation<br/>- Security filtering]
        EI[EffectInterpreter<br/>- Effect execution<br/>- Capability checking<br/>- I/O handling]
    end
    
    subgraph "Actor System (Business Logic)"
        AS[ActorSystem<br/>- Lifecycle management<br/>- Supervision<br/>- Actor creation]
        
        subgraph "Foundation Actors"
            HA[HealthMonitorActor]
            MA[MetricsCollectorActor] 
            RVA[RuntimeVerificationActor]
            EMA[EnergyManagementActor]
            DA[DashboardActor]
        end
        
        subgraph "Meta-System Actors"
            PCA[PersonaCoreActor]
            AA[AnalystActor]
            PA[PredictorActor]
        end
    end
    
    BAB[BusActorBridge<br/>- Channel subscriptions<br/>- Message routing<br/>- Protocol conversion]
    
    %% Message flow
    HMB -->|"subscribe(channel)"| BAB
    BAB -->|"tell(message)"| AS
    AS -->|"deliver_message()"| HA
    AS -->|"deliver_message()"| MA
    
    %% Actor responses  
    HA -->|"returns Effect"| EI
    MA -->|"returns Effect"| EI
    
    %% Effect results
    EI -->|"publishes Event"| HMB
```

### Current Actor Integration Status

#### ✅ **COMPLETED**:
- **Foundation Actors**: All monitoring, metrics, verification, energy, and dashboard services converted to supervised actors
- **Bus-Actor Bridge**: Routes messages from bus channels to actor mailboxes
- **Actor System Integration**: Proper spawning, supervision, and lifecycle management
- **Effect-Based I/O**: All actors return declarative Effects instead of performing I/O
- **Thread Elimination**: All 16 thread-based files deprecated and replaced with actor-compatible implementations

#### 🟡 **PARTIAL**:
- **Command Handlers**: Still plain async functions, not actors (by design for stateless operations)
- **EffectInterpreter**: Central service, not an actor (intentionally stateless and centralized)

#### ❌ **REMAINING WORK**:
- **Meta-System Actors**: PersonaCore, Analyst, Predictor need actor conversion
- **WaveTrace Integration**: Actor mailbox depths not yet recorded
- **Choreography Support**: No choreo_id propagation

### Actor Standards

#### 1. **Base Actor** (Simple Pattern)
```python
from person_suit.core.actors.base import Actor, StandardActorMessage

class HealthMonitorActor(Actor):
    async def receive(self, message: StandardActorMessage) -> Optional[Effect]:
        if message.type == "health_check":
            return CheckHealth(component="memory", check_id="mem_001")
```

#### 2. **DecoupledActor** (CAW-Aware Pattern)  
```python
from person_suit.core.actors.caw_actor import DecoupledActor

class PersonaCoreActor(DecoupledActor):
    async def receive_with_context(
        self, 
        message: StandardActorMessage,
        context: UnifiedContext,
        fidelity: int
    ) -> None:
        # Business logic with context and fidelity awareness
```

## From Orchestration to Choreography

The current architecture supports both **Orchestration** (via central EffectInterpreter) and emerging **Choreography** patterns:

### Current State: Hybrid Orchestration-Choreography
- **Orchestration**: EffectInterpreter centrally executes effects
- **Choreography Elements**: Actors interact via bus without central control
- **Message Flows**: Decentralized routing through channel subscriptions

### Evolution Path
1. **Phase 1** (Current): Bus + Actors with central effect interpretation
2. **Phase 2** (Next): Choreography DSL compilation to bus subscriptions  
3. **Phase 3** (Future): Full choreographic protocols with formal verification

```python
# Future choreography example
@choreography("user_registration.cho")
class UserRegistrationFlow:
    participants = ["Gateway", "UserService", "DatabaseService"]
    
    def flow(self):
        gateway.send("user_data", to=user_service)
        user_service.send("store_user", to=database_service)
        database_service.send("user_stored", to=gateway)
```

The `HybridMessageBus` provides the perfect foundation for choreography through:
- **Decoupled Communication**: No direct service dependencies
- **Channel-Based Routing**: Protocol-agnostic message delivery
- **Capability-Aware Security**: Participants only receive authorized messages
- **Provenance Tracking**: Full audit trail of choreographic interactions

## Migration Strategy

### Step 1: Parallel Operation (Week 1)
- Run new message bus alongside existing system
- Shadow traffic to new bus for testing
- No production dependencies

### Step 2: Service by Service (Week 2-3)
```python
# Example: Migrating Memory Service

# OLD: Direct import and call
from person_suit.core.memory import MemorySystem
memory = MemorySystem()
result = memory.store(data)

# NEW: Message-based
msg = HybridMessage(
    channel="pc.memory.store",
    payload={"data": data}
)
result = await message_bus.send(msg)
```

### Step 3: Remove Old Code (Week 4)
- Delete old direct-import services
- Remove legacy adapters
- Update all tests

## Validation Plan

### 1. Unit Tests
```python
# Test: A handler must be pure and return an Effect
def test_handler_returns_effect():
    handler = MemoryHandler()
    command = HybridMessage(channel="command.pc.memory.encode", ...)
    result_message = handler.handle_encode(command)
    
    assert result_message.message_type == MessageType.EFFECT
    assert "WriteDatabaseEffect" in result_message.payload
```

### 2. Integration Tests
```python
# Test: Full Command -> Effect -> Event flow
async def test_full_command_flow():
    bus = await get_message_bus()
    interpreter = await get_interpreter() # Setup interpreter
    
    final_events = []
    await bus.subscribe("event.pc.memory.encoded", lambda m: final_events.append(m))
    
    # Send the initial command
    command = HybridMessage(channel="command.pc.memory.encode", ...)
    await bus.publish(command)
    
    await asyncio.sleep(0.1) # Allow time for processing
    
    assert len(final_events) == 1
    assert final_events[0].payload["memory_id"] is not None
```

### 3. Performance Tests
```python
# Test: Message throughput under load
async def test_throughput():
    bus = await get_message_bus()
    
    start = time.time()
    messages_sent = 0
    
    # Send 10,000 messages
    for i in range(10000):
        msg = HybridMessage(
            channel=f"test.perf.{i % 10}",
            payload={"index": i}
        )
        await bus.send(msg)
        messages_sent += 1
    
    duration = time.time() - start
    throughput = messages_sent / duration
    
    assert throughput > 1000  # At least 1000 msg/sec
```

### 4. Chaos Testing
```python
# Test: System behavior under failure
async def test_handler_failure_recovery():
    # Register failing handler
    @message_handler("test.chaos.fail")
    async def failing_handler(msg):
        if random.random() > 0.5:
            raise Exception("Random failure")
        return {"status": "ok"}
    
    # Send messages and verify retry/recovery
    success_count = 0
    for i in range(100):
        msg = HybridMessage(
            channel="test.chaos.fail",
            payload={"attempt": i}
        )
        try:
            result = await bus.send(msg)
            if result.payload.get("status") == "ok":
                success_count += 1
        except:
            pass
    
    # Should succeed ~50% + retries
    assert success_count > 60
```

## Performance Considerations

### Message Size Optimization
```python
import json
import zlib

def compress_payload(payload: dict) -> bytes:
    """Compresses a message payload if it's over a certain size."""
    payload_bytes = json.dumps(payload).encode('utf-8')
    if len(payload_bytes) > 1024:  # 1KB threshold
        return zlib.compress(payload_bytes)
    return payload_bytes

# This logic would be applied by the sender before putting the message on the bus,
# particularly for network-bound messages in a deployment profile.
```

### Channel Subscription Caching
```python
class ChannelSubscriptionCache:
    """Cache channel->handler mappings for performance"""
    
    def __init__(self):
        self._cache = {}
        self._ttl = 60  # seconds
        
    def get_handlers(self, channel: str) -> List[Handler]:
        if channel in self._cache:
            entry = self._cache[channel]
            if time.time() - entry["timestamp"] < self._ttl:
                return entry["handlers"]
        
        # Cache miss - fetch and cache
        handlers = self._fetch_handlers(channel)
        self._cache[channel] = {
            "handlers": handlers,
            "timestamp": time.time()
        }
        return handlers
```

### Priority Queue Implementation (As-Built)

The actual implementation uses a single, more flexible `asyncio.PriorityQueue` with **integer priorities** for maximum computational efficiency. This allows for granular, numeric priorities using the fixed-point scale (0 to 1,000,000), where higher numbers are processed first.

```python
# From person_suit/core/infrastructure/hybrid_message_bus.py

class PriorityMessageQueue:
    """ACF-aware priority queue for messages using integer priorities."""
    
    def __init__(self, deployment_profile: DeploymentProfile):
        self.profile = deployment_profile
        # Use a single priority queue with integer priorities
        self._queue = asyncio.PriorityQueue(maxsize=deployment_profile.max_queue_depth)
        # ...
    
    async def put(self, message: HybridMessage) -> bool:
        """Add message to queue based on integer priority."""
        priority = message.priority  # An integer from 0 to SCALE (1,000,000)
        # ... ACF logic to handle full queues ...
        
        # Use negative priority for max-heap behavior in the min-heap queue
        await self._queue.put((-priority, message.timestamp, message))
        return True

    async def get(self) -> Optional[HybridMessage]:
        """Get next highest priority message."""
        # ...
```

This integer-based approach provides significant computational benefits:
- **No floating-point arithmetic** in priority comparisons
- **Exact precision** for priority ordering without rounding errors  
- **Faster comparisons** using integer operations
- **Consistent serialization** across different systems and languages
- **Memory efficiency** with fixed-size integer storage

### ACF Integer Arithmetic Examples

All ACF calculations now use integer arithmetic for maximum efficiency:

```python
# Example: High-load fidelity degradation
base_fidelity = 800_000  # 0.8 in integer scale
load_factor_int = 900_000  # 0.9 load in integer scale

# Old (float): adapted = base * (1 - load * 0.5)
# New (integer): adapted = base - (base * load) // (2 * SCALE)
degradation = (base_fidelity * load_factor_int) // (2 * SCALE)
adapted_fidelity = max(min_fidelity, base_fidelity - degradation)
# Result: 440_000 (0.44 fidelity) - computed with pure integer ops

# Example: Priority-based fidelity boost
if priority_bucket >= PRIO_HIGH:  # 800_000
    # 10% boost using integer arithmetic
    adapted_fidelity = min(SCALE, (base_fidelity * 11) // 10)
```

### Load Factor Calculations

System load calculations now use integer scale throughout:

```python
# Convert queue depth to load factor in integer scale
queue_depth = 9  # messages
max_queue = 10   # capacity
load_factor_int = (queue_depth * SCALE) // max_queue  # 900_000 (0.9 load)

# Define thresholds in integer scale for fast comparisons
HIGH_LOAD_THRESHOLD = int(0.6 * SCALE)  # 600_000
CACHE_THRESHOLD = int(0.6 * SCALE)      # 600_000  
BATCH_THRESHOLD = int(0.7 * SCALE)      # 700_000

# Fast integer comparisons (no float arithmetic)
use_cache = load_factor_int > CACHE_THRESHOLD
should_batch = load_factor_int > BATCH_THRESHOLD
```

## Example Scenarios

### Scenario 1: High-Load Adaptation
```
Time: T0 - System at 30% CPU
  Message: pc.memory.encode (fidelity=0.9)
  Action: Process normally

Time: T1 - System at 85% CPU (spike)
  Message: pc.memory.encode (fidelity=0.9)
  Action: Reduce to min_fidelity=0.5, use simple encoder

Time: T2 - System at 90% CPU (sustained)
  Message: pc.memory.encode (fidelity=0.9)
  Action: Queue for later or reject with backpressure
```

### Scenario 2: Cross-System Coordination
```
User Request → Gateway → Command Bus → PersonaCore
                                    ↓
                            Effect: UpdateMemory
                                    ↓
                            EffectInterpreter
                                    ↓
                            Event: MemoryUpdated
                                    ↓
                    Analyst ← Event Bus ← Predictor

All coordinated through channels, no direct calls!
```

### Scenario 3: Graceful Degradation
```
Normal Operation:
  pc.emotion.analyze → Full sentiment analysis
  
Under Load (ACF=0.3):
  pc.emotion.analyze → Basic keyword matching
  
Critical Load (ACF=0.1):
  pc.emotion.analyze → Return cached/default
```

## Success Metrics

1. **Zero Import Errors**: No circular dependencies
2. **Message Throughput**: >1000 msg/sec sustained
3. **Latency**: <10ms for priority messages
4. **ACF Effectiveness**: ≥50% resource reduction at 0.5 fidelity
5. **Test Coverage**: >90% for message paths
6. **Provenance Coverage**: 100% of messages recorded in the log, <0.5% trace loss
7. **Migration Success**: All services using messages
8. **WaveTrace Completeness**: 100% messages have trace context
9. **CAW Branch Tracking**: All adaptation decisions recorded

## Risk Mitigation

1. **Performance Risk**: Cache channel subscriptions
2. **Complexity Risk**: Comprehensive logging/tracing
3. **Migration Risk**: Parallel operation period
4. **Debugging Risk**: Message flow visualization tools
5. **WaveTrace Overhead**: Async writers with batching

## Provenance & Observability

The CAW Emergence Rule mandates _measurable_ behaviour.  Therefore the bus must
provide an **immutable, query-first provenance trail** for every Message →
Effect → Event transition.

### 10.1 Event Sourcing Backbone
All `HybridMessage` instances **default** to `record_provenance=True`.  The Bus
writer appends them to a high-throughput log (Native WaveTrace, Redpanda, or Pulsar).
Consumers use CDC streams to populate:

* **Temporal Property Graph** (Neo4j + Timescale) – causality queries
* **Columnar OLAP** (DuckDB / ClickHouse) – latency & throughput dashboards

### 10.2 WaveTrace Integration

WaveTrace provides comprehensive observability through:

#### Core Tracking
- **Message Lifecycle**: Creation → Queue → Dispatch → Handler → Result
- **Trace Context**: trace_id, span_id, parent_span_id propagation
- **Timing Metrics**: Queue time, processing time, total latency
- **Handler Results**: Success/failure, error messages, retry attempts

#### CAW-Specific Observability
- **Wave-Particle Decisions**: Which computational branch was taken
- **Fidelity Adaptations**: When and why fidelity was adjusted
- **Context Propagation**: Differential updates and their impact
- **Capability Checks**: Security decisions and denials

#### Actor Integration
- **Mailbox Depths**: Queue buildup in actors
- **Supervision Events**: Restarts, failures, recovery times
- **Resource Usage**: CPU/memory per actor

#### Choreography Support
- **Flow Identification**: choreo_id stamping
- **Step Tracking**: Progress through choreographic sequences
- **Participant Coordination**: Inter-service message flows

### 10.3 eBPF & USDT Tracepoints
Kernel-level probes capture non-Python handlers (e.g., Zig micro-services
compiled to WASM with Wasmtime) ensuring **full-stack traces** without code
changes.

### 10.4 Grafana Dashboards
Dashboards show:
* ACF curves versus load
* Capability check pass/fail heatmaps
* Wave-particle ratio distribution across channels
* End-to-end latency CDF per message_type
* Actor mailbox depth histograms
* Choreography flow completion times

### 10.5 Provenance Query Example

```cypher
// Neo4j query for trace reconstruction
MATCH p=(m:Message {correlation_id:$cid})-[:CAUSES*]->(e:Effect)
RETURN p

// DuckDB query for performance analysis
SELECT 
    channel,
    percentile_cont(0.99) WITHIN GROUP (ORDER BY latency_ms) as p99_latency,
    COUNT(*) as message_count,
    SUM(CASE WHEN wave_branch_taken THEN 1 ELSE 0 END) as wave_decisions,
    SUM(CASE WHEN particle_branch_taken THEN 1 ELSE 0 END) as particle_decisions
FROM wavetrace_spans
WHERE timestamp > now() - interval '1 hour'
GROUP BY channel
ORDER BY p99_latency DESC;
```

### 10.6 Architectural Guardrails
1.  **Every interpreter** must write a `provenance_completion` record after
    executing an Effect – success or failure.
2.  **Dashboards** are part of the CI pipeline; a PR fails if new metrics are
    missing.
3.  **WaveTrace spans** must include CAW branch decisions for principle verification.

## 11. Implementation Optimisation & Hardening Roadmap (Updated 2025-06-22)

The core architecture remains **sound** but the review surfaced pragmatic
library and integration upgrades that significantly improve latency,
observability and zero-trust posture while remaining 100 % CAW-compliant.

### 11.1  Faster Kafka Path
* Replace `aiokafka` with `confluent-kafka-python` (librdkafka).
* BusKernel producer/consumer wrappers become thin delegates mapping
  `HybridMessage` → `Producer.produce()` / `Consumer.poll()`.
* Idempotent producer + exactly-once semantics now native; simplifies
  provenance sink retry logic.

### 11.2  Actor Supervision Hooks with WaveTrace
* New `core/actors/supervisor.py` offers restart, back-off, metrics.
* `HybridMessageBus.subscribe()` registers each handler's **actor_id** so
  Supervisor can emit `actor_mailbox_depth` and restart crashed handlers
  without dropping messages.
* **WaveTrace Integration**: All supervision events recorded with trace context

### 11.3  Biscuit Capability Tokens
* `HybridMessage.capability_token: bytes` now carries a Biscuit v2 token.
* `CapabilityManager.has()` becomes `CapabilityVerifier.verify(biscuit,
  required_capability, caveats)`; denial increments `invalid_biscuit_total`.
* **Audit Trail**: All capability decisions recorded in WaveTrace

### 11.4  Choreography Provenance
* Compiler stamps `msg.headers['choreo_id'] = <uuid>` per step; interpreter
  and bus propagate untouched.  Provenance graph now groups messages by
  choreography flow for end-to-end latency queries.

### 11.5  Counter-Evidence Automation
* The scripts in `scripts/counter_evidence/` (load, penetration, context-diff)
  run in CI (`ci/probes.yaml`).  Any failure blocks merge ensuring the
  *Counterevidence Methodology* stays enforced.

### 11.6  Canonical Directory Map (to avoid duplications)

| Concern | Canonical Package | Notes |
|---------|------------------|-------|
| Hybrid Message Bus runtime | `person_suit.core.infrastructure.hybrid_message_bus` | Single entry‐point `HybridMessageBus` and priority queue implementation. |
| Command / Effect / Event handler registry | `person_suit.core.handlers.registry` | Registry singleton + decorator. |
| EventBus (simple pub/sub used by shared-layer tests) | `person_suit.shared.events.bus` | The placeholder added in Sprint-1. All other `EventBus` duplicates (`shared.utils.patterns.EventBus`, `meta_systems.*.EventBus`) are scheduled for deletion or refactor in Sprint-4 legacy cleanup. |
| Effect system core (Effect base, interpreter, strategies) | `person_suit.core.effects` | Sub-packages: `base_effect.py`, `effect_interpreter.py`, `handlers/`. |
| Dual-state helpers (Wave/Particle) | `person_suit.core.infrastructure.dual_wave` | Will be merged into `core/information/dual.py` in Sprint-6. |
| WaveTrace provenance backend | `person_suit.core.provenance.backends.native` | Native segment-based storage with full trace context support. |

During Sprint-4 "Legacy Deletion & Docs" all non-canonical duplicates are either removed or turned into thin re-exports pointing to the canonical path.  This table should help new contributors (and automated agents) avoid importing from the wrong location.

>  These optimisations do **not** change message schemas or CAW principles and
>  are fully wire-compatible with existing meta-systems.

## Conclusion

This hybrid, effect-aware approach provides:
- ✅ Clear separation of concerns (Command vs. Effect vs. Event)
- ✅ Centralized, secure, and adaptive side-effect execution
- ✅ A practical path from orchestration to true choreography
- ✅ Complete decoupling and enhanced testability
- ✅ Full alignment with CAW and v3 Universal Architectural Principles
- ✅ Comprehensive observability through WaveTrace integration

The implementation focuses on practicality while providing a solid foundation for the powerful theoretical paradigms that define the PersonSuit project. 

For example, a **fidelity** of *0.85* becomes the integer bucket `850 000` (`0.85 × SCALE`).  All APIs now
*require* and *return* those integer buckets – *never* raw floats – ensuring µ-resolution precision across
serialization boundaries (JSON, Protobuf, etc.).

```python
# Fixed-point constants (core/constants/fixed_point_scale.py)

SCALE = 1_000_000  # 1.000000 → full-scale, 0 → none

# Priority buckets
PRIO_CRITICAL = SCALE          # 1_000_000
PRIO_HIGH     = int(0.8*SCALE) #   800_000
PRIO_NORMAL   = int(0.5*SCALE) #   500_000
PRIO_LOW      = int(0.2*SCALE) #   200_000

# Fidelity buckets (same scale)
FIDELITY_MAX  = SCALE          # 1_000_000
FIDELITY_HIGH = int(0.8*SCALE) #   800_000
FIDELITY_MED  = int(0.5*SCALE) #   500_000
FIDELITY_LOW  = int(0.2*SCALE) #   200_000
FIDELITY_MIN  = 0
```

Throughout the document any threshold previously expressed as a float (e.g. *0.9*) should be mentally
converted to its bucket counterpart (e.g. *0.9 → 900 000*).  This matches the **Differential Context
Propagation Rule** which mandates uniform integer scales for diff computation and guarantees stable
serialization across heterogeneous runtimes (Python ↔ Zig ↔ WASM).